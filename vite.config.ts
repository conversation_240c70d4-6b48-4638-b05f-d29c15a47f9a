import process from 'node:process';
import { URL, fileURLToPath } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig(configEnv => {
  const viteEnv = loadEnv(configEnv.mode, process.cwd()) as unknown as ImportMetaEnv;

  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        '~': fileURLToPath(new URL('./', import.meta.url)),
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    plugins: [
      vue(),
      tailwindcss()
    ],
    server: {
      strictPort: true,
      allowedHosts: ['frp.f5pay.one'],
      host: '0.0.0.0',
      port: 9527,
      open: true,
      proxy: {
        '/hubs/gaming': {
          target: viteEnv.VITE_BASE_URL,
          changeOrigin: true,
          ws: true
        },
        '/api': {
          target: viteEnv.VITE_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/lottery': {
          target: viteEnv.VITE_BASE_URL, // http://*************:5065
          changeOrigin: true,
        }
      }
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP,
      commonjsOptions: {
        ignoreTryCatch: false
      }
    }
  };
});

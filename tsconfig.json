{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["./src/*"],
      "~/*": ["./*"]
    },
    "resolveJsonModule": true,
    "types": [ "node", "vite/client", "vue" ],
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": false,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "allowJs": true,
    "skipLibCheck": true
  },
  "include": [
    "vite.config.ts",
    "tailwind.config.ts",
    "src/**/*.ts", 
    "src/**/*.tsx", 
    "src/**/*.vue", 
  ],
  "exclude": ["node_modules", "dist"]
}

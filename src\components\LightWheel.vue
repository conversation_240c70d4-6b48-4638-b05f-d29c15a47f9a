// WheelAnimation.vue
<script setup lang="ts">
import { ref, onUnmounted, watch, computed, onMounted } from 'vue'
import { useSoundBuffer } from '../composables/useSoundBuffer'
import { useBetting } from '@/composables/useBetting'
import { useChipStore } from '../stores/chip'
import { useGameStore } from '@/stores/game'
import type { LotteryOrderSuccessEto } from '@/scripts/signalr/hubs/default-hub'

import gsap from 'gsap'
import TabBar from './TabBar.vue'
import ChatInfo from './ChatBar.vue'
// 类型定义
type Suit = 'hearts' | 'diamonds' | 'clubs' | 'spades'
type SuitArea = 'heart' | 'diamond' | 'club' | 'black' | null
type SizeArea = 'Large' | 'Small' | null
type BetType = 'rank' | 'suit' | 'size'
type Card = {
  suit: Suit
  rank: string
  display: string
}
const isDev = import.meta.env.DEV

const gameStore = useGameStore()
const chipStore = useChipStore()
gameStore.revealed = true

// 获取游戏数据的计算属性
const sizeGameData = computed(() => {
  const large = gameStore.sizeGameTypes.find(game => game.playingTypeId === 'Large')
  const small = gameStore.sizeGameTypes.find(game => game.playingTypeId === 'Small')
  return { large, small }
})
// 动画状态
const currentSegment = ref<number>(-1) // 默认为-1表示不选中任何分段
const isAnimating = ref<boolean>(false)
const showHighlight = ref<boolean>(false) // 默认不显示高亮

// 高亮状态
const highlightSuit = ref<SuitArea>(null)
const highlightSize = ref<SizeArea>(null)

// 当前选中的牌
const currentCard = ref<Card | null>(null)

// 添加投注功能
// 使用Map存储所有投注的区域和对应的投注值
const betAmounts = ref(new Map())

//声音
const { play } = useSoundBuffer()
// 提交投注
const { submitBet, canBet } = useBetting()

// 用于跟踪高亮定时器的ID - 使用明确的类型定义
let highlightTimerId: ReturnType<typeof setTimeout> | null = null;

// 获取某个区域的投注值，若没有则返回空字符串
const getBetAmount = (type: BetType, id: string | number): string => {
  const key = `${type}-${id}`
  const amount = betAmounts.value.get(key)
  // 只有当投注过后才返回数值，否则返回空字符串
  return amount ? amount.toString() : ""
}

// 清空投注
const clearAllBetAmounts = () => {
  console.log('清空所有投注金额')
  betAmounts.value = new Map()
}

/**
 * 处理投注成功事件
 * 根据 Socket 信号更新投注金额显示
 */
const handleBetSuccess = (event: CustomEvent<LotteryOrderSuccessEto>) => {
  const data = event.detail
  console.log('🎉 [LightWheel] 收到投注成功确认:', data)

  // 根据投注的玩法类型和金额更新显示
  const { playingTypeName, bettingNumber, amount } = data

  console.log('🔍 [LightWheel] 解析投注信息:', {
    playingTypeName,
    bettingNumber,
    amount
  })

  // 需要根据 playingTypeName 和 bettingNumber 确定投注的类型和位置
  let betType: BetType
  let betId: string | number

  // 根据玩法名称判断投注类型
  if (playingTypeName === '大' || playingTypeName === '小') {
    betType = 'size'
    betId = playingTypeName === '大' ? 'Large' : 'Small'
    console.log('📏 [LightWheel] 识别为大小投注:', betType, betId)
  } else if (playingTypeName.includes('数字')) {
    betType = 'rank'
    // 从 bettingNumber 数组中获取数字
    const number = bettingNumber[0]
    // 将数字转换为对应的索引（数字形式，与模板中的索引匹配）
    const rankMap = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
    const index = rankMap.indexOf(number)

    if (index === -1) {
      console.error('❌ [LightWheel] 无法找到数字映射:', number, '在', rankMap)
      return
    }

    betId = index // 使用数字索引，与模板中的索引匹配
    console.log('🔢 [LightWheel] 识别为数字投注:', betType, betId, '(', number, ')', 'index:', index)
    console.log('🔢 [LightWheel] rankMap:', rankMap)
  } else if (playingTypeName.includes('黑桃') || playingTypeName.includes('黑')) {
    betType = 'suit'
    betId = 'black'
    console.log('♠️ [LightWheel] 识别为黑桃投注:', betType, betId)
  } else if (playingTypeName.includes('红桃') || playingTypeName.includes('红')) {
    betType = 'suit'
    betId = 'heart'
    console.log('♥️ [LightWheel] 识别为红桃投注:', betType, betId)
  } else if (playingTypeName.includes('梅花') || playingTypeName.includes('梅')) {
    betType = 'suit'
    betId = 'club'
    console.log('♣️ [LightWheel] 识别为梅花投注:', betType, betId)
  } else if (playingTypeName.includes('方块') || playingTypeName.includes('方')) {
    betType = 'suit'
    betId = 'diamond'
    console.log('♦️ [LightWheel] 识别为方块投注:', betType, betId)
  } else {
    console.warn('❌ [LightWheel] 未知的玩法类型:', playingTypeName)
    console.warn('❌ [LightWheel] 完整数据:', data)
    return
  }

  // 更新投注金额显示
  const key = `${betType}-${betId}`
  const currentAmount = betAmounts.value.get(key) || 0
  const newAmount = currentAmount + amount
  betAmounts.value.set(key, newAmount)

  console.log(`💰 [LightWheel] ${betType} ${betId} 的投注金额更新为: ${newAmount}`)
  console.log(`🔑 [LightWheel] 使用的key: ${key}`)
}

// 统一的点击处理函数
// const handleClick = (type: BetType, id: number | string, event?: Event) => {
//   play('bet', 0.8)
//   if (isAnimating.value) return

//   // 阻止事件冒泡
//   if (event) {
//     event.stopPropagation()
//   }

//   // 增加对应区域的投注
//   const key = `${type}-${id}`
//   const currentAmount = betAmounts.value.get(key) || 0
//   // const newAmount = currentAmount + 1
//   const chipValue = chipStore.selectedChip || 1




//   const newAmount = currentAmount + chipValue
//   betAmounts.value.set(key, newAmount)
//   console.log(`${type} ${id} 的投注增加到 ${newAmount}`)

//   // 重置所有高亮状态
//   currentSegment.value = -1
//   highlightSuit.value = null
//   highlightSize.value = null

//   // 根据类型设置相应的高亮
//   if (type === 'rank') {
//     currentSegment.value = id as number
//   } else if (type === 'suit') {
//     highlightSuit.value = id as SuitArea
//   } else if (type === 'size') {
//     highlightSize.value = id as SizeArea
//   }

//   showHighlight.value = true

//   // 清除之前的定时器（如果存在）
//   if (highlightTimerId !== null) {
//     clearTimeout(highlightTimerId)
//   }

//   // 设置新的定时器清除高亮
//   highlightTimerId = setTimeout(() => {
//     // 只有在不处于动画状态时才清除高亮
//     if (!isAnimating.value) {
//       showHighlight.value = false
//       if (type === 'rank') {
//         currentSegment.value = -1
//       } else if (type === 'suit') {
//         highlightSuit.value = null
//       } else if (type === 'size') {
//         highlightSize.value = null
//       }
//     }
//     highlightTimerId = null;
//   }, 500)
// }
const handleClick = async (type: BetType, id: number | string, event?: Event) => {
  if (isAnimating.value) return

  if (event) {
    event.stopPropagation()
  }

  // 🔥 提前检查投注状态，给用户更好的反馈
  const betStatus = canBet()
  if (!betStatus.canPlace) {
    console.warn(`⚠️ ${betStatus.message}`)
    // 状态消息已经在 canBet() 中自动显示到 HeaderBar 了
    return
  }

  play('bet', 0.8)

  // 🔥 使用封装的投注函数
  const success = await submitBet(type, id)

  if (!success) {
    // 投注失败，不继续执行
    return
  }

  // 🔥 投注请求已发送，但不立即更新本地状态
  // 等待 Socket 信号确认成功后再更新投注金额显示
  console.log(`� 投注请求已发送: ${type} ${id}`)

  // 原有的高亮逻辑保持不变
  currentSegment.value = -1
  highlightSuit.value = null
  highlightSize.value = null

  if (type === 'rank') {
    currentSegment.value = id as number
  } else if (type === 'suit') {
    highlightSuit.value = id as SuitArea
  } else if (type === 'size') {
    highlightSize.value = id as SizeArea
  }

  showHighlight.value = true

  if (highlightTimerId !== null) {
    clearTimeout(highlightTimerId)
  }

  highlightTimerId = setTimeout(() => {
    if (!isAnimating.value) {
      showHighlight.value = false
      if (type === 'rank') {
        currentSegment.value = -1
      } else if (type === 'suit') {
        highlightSuit.value = null
      } else if (type === 'size') {
        highlightSize.value = null
      }
    }
    highlightTimerId = null;
  }, 500)
}
// 分段数量常量
const SEGMENT_COUNT = 13

// 花色和符号映射
const SUIT_SYMBOLS: Record<Suit, string> = {
  spades: '♠',
  hearts: '♥',
  clubs: '♣',
  diamonds: '♦'
}

// 花色到区域的映射
const SUIT_TO_AREA: Record<Suit, SuitArea> = {
  spades: 'black',
  hearts: 'heart',
  clubs: 'club',
  diamonds: 'diamond'
}

// 获取花色的中文名称
const getSuitChineseName = (suit: Suit): string => {
  switch (suit) {
    case 'spades': return '黑桃';
    case 'hearts': return '红心';
    case 'clubs': return '梅花';
    case 'diamonds': return '方块';
    default: return '';
  }
}

// 获取牌面的中文名称
const getRankChineseName = (rank: string): string => {
  switch (rank) {
    case 'A': return 'A';
    case 'J': return 'J';
    case 'Q': return 'Q';
    case 'K': return 'K';
    default: return rank;
  }
}

// 牌面到分段的映射
const RANK_TO_SEGMENT: Record<string, number> = {
  'A': 0,
  '2': 1,
  '3': 2,
  '4': 3,
  '5': 4,
  '6': 5,
  '7': 6,
  '8': 7,
  '9': 8,
  '10': 9,
  'J': 10,
  'Q': 11,
  'K': 12
}

// 创建一副牌
const createDeck = (): Card[] => {
  const suits: Suit[] = ['spades', 'hearts', 'clubs', 'diamonds']
  const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
  const deck: Card[] = []

  for (const suit of suits) {
    for (const rank of ranks) {
      deck.push({
        suit,
        rank,
        display: `${SUIT_SYMBOLS[suit]}${rank}`
      })
    }
  }

  return deck
}

// 获取大小区域
const getSizeArea = (rank: string): SizeArea => {
  if (['A', '2', '3', '4', '5', '6'].includes(rank)) {
    return 'Small'
  } else if (['8', '9', '10', 'J', 'Q', 'K'].includes(rank)) {
    return 'Large'
  }
  return null // 7不高亮
}

// // 随机抽取一张牌
// const drawRandomCard = (): Card => {
//   const deck = createDeck()
//   const randomIndex = Math.floor(Math.random() * deck.length)
//   return deck[randomIndex]
// }


const getCardFromResult = (): Card | null => {
  const [suitRank, rank] = gameStore.result
  if (!suitRank || !rank) return null

  const suitCode = suitRank[1] // 'D' in "SD"
  const suitMap: Record<string, Suit> = {
    S: 'spades',
    H: 'hearts',
    D: 'diamonds',
    C: 'clubs',
  }

  const suit = suitMap[suitCode.toUpperCase()]
  if (!suit) return null

  return {
    suit,
    rank,
    display: `${SUIT_SYMBOLS[suit]}${rank}`,
  }
}

// 开始动画
const startAnimation = () => {
  if (isAnimating.value) return

  // 清除任何可能存在的高亮定时器
  if (highlightTimerId !== null) {
    clearTimeout(highlightTimerId)
    highlightTimerId = null
  }

  isAnimating.value = true
  showHighlight.value = true

  // 重置高亮区域
  highlightSuit.value = null
  highlightSize.value = null
  currentSegment.value = -1

  // 抽取随机牌
  // const card = drawRandomCard()

  // store 结果
  const card = getCardFromResult()
  if (!card) return
  currentCard.value = card


  // 在控制台打印抽取的牌
  console.log('-------- 抽取的扑克牌 --------')
  console.log(`花色: ${getSuitChineseName(card.suit)} (${card.suit})`)
  console.log(`点数: ${getRankChineseName(card.rank)}`)
  console.log(`显示: ${card.display}`)
  console.log(`对应分段索引: ${RANK_TO_SEGMENT[card.rank]}`) // 打印目标分段

  // 打印大小区域
  const sizeArea = getSizeArea(card.rank)
  if (sizeArea) {
    console.log(`区域: ${sizeArea === 'Large' ? '大' : '小'}`)
  } else {
    console.log(`区域: 无 (7不属于大小区域)`)
  }
  console.log('----------------------------')

  // 确定目标位置
  const targetSegment = RANK_TO_SEGMENT[card.rank]
  console.log(`当前分段: ${currentSegment.value}, 目标分段: ${targetSegment}`)

  // 动画参数
  const totalSpins = 4 // 完整旋转圈数

  // 计算需要旋转的总段数
  // 起始位置到目标位置的距离 + 额外的完整圈数
  const distanceToTarget = (targetSegment - currentSegment.value + SEGMENT_COUNT) % SEGMENT_COUNT
  const totalSegments = totalSpins * SEGMENT_COUNT + distanceToTarget

  console.log(`需要旋转: ${totalSegments} 段, 约 ${(totalSegments / SEGMENT_COUNT).toFixed(2)} 圈`)

  // 使用一个对象来存储当前旋转的段数
  let rotatedSegments = 0

  // 使用GSAP创建动画
  const lastSlowSteps = 4
  const totalSlowStart = (targetSegment - lastSlowSteps + SEGMENT_COUNT) % SEGMENT_COUNT


  // 💡 先快速转到 slowStart 之前的格子
  const fastSegments = (totalSpins * SEGMENT_COUNT) + ((totalSlowStart - currentSegment.value + SEGMENT_COUNT) % SEGMENT_COUNT)

  gsap.to({}, {
    duration: 4.5,
    ease: 'none',
    onUpdate: function () {
      const progress = this.progress()
      const currentRotation = fastSegments * progress
      const newRotated = Math.floor(currentRotation)

      if (newRotated > rotatedSegments) {
        for (let i = 0; i < newRotated - rotatedSegments; i++) {
          currentSegment.value = (currentSegment.value + 1) % SEGMENT_COUNT
          play('drop', 0.7)
        }
        rotatedSegments = newRotated
      }
    },
    onComplete: () => {
      // ✅ 手动慢慢播放最后4个格子
      const delayList = [200, 300, 400, 500]
      let step = 0

      const manualStep = () => {
        currentSegment.value = (currentSegment.value + 1) % SEGMENT_COUNT
        play('drop', 0.7)
        step++

        if (step < lastSlowSteps) {
          setTimeout(manualStep, delayList[step])
        } else {
          // ✅ 最后精准到 target
          currentSegment.value = targetSegment
          isAnimating.value = false

          // ✅ 通知 TimeBar.vue 可以翻牌了
          gameStore.revealed = true
          setTimeout(() => blinkAndRemoveHighlight(), 200)
        }
      }

      // 🚀 启动慢速阶段
      setTimeout(manualStep, delayList[0])
    }
  })


  // gsap.to({}, {
  //   duration: 5, // 总动画时长5秒
  //   ease: "power2.out", // 改用power2.out让减速更自然
  //   onUpdate: function () {
  //     // 获取当前进度
  //     const progress = this.progress()

  //     // 计算当前应该旋转了多少段
  //     // 优化动画曲线：前20%时加速，20%-75%匀速，最后25%减速
  //     let currentRotation

  //     if (progress < 0.2) {

  //       // 加速阶段 - 使用三次曲线加速，更平滑
  //       const phaseProgress = progress / 0.2 // 归一化到0-1
  //       currentRotation = totalSegments * 0.2 * (phaseProgress * phaseProgress * phaseProgress)
  //     } else if (progress < 0.65) {

  //       // 匀速阶段 - 线性
  //       const phaseProgress = (progress - 0.2) / 0.55 // 归一化到0-1
  //       currentRotation = totalSegments * 0.2 + (totalSegments * 0.55 * phaseProgress)
  //     } else {

  //       // 减速阶段 - 使用更平滑的缓动函数
  //       const phaseProgress = (progress - 0.75) / 0.25 // 归一化到0-1
  //       // 使用自定义缓动函数来实现更平滑的减速
  //       const easedProgress = 1 - Math.pow(1 - phaseProgress, 3)
  //       currentRotation = totalSegments * 0.75 + (totalSegments * 0.25 * easedProgress)
  //     }

  //     // 计算当前应该旋转的段数（取整）
  //     const newRotatedSegments = Math.floor(currentRotation)

  //     // 如果有新的段要旋转
  //     if (newRotatedSegments > rotatedSegments) {
  //       // 计算新增的段数
  //       const segmentsToAdd = newRotatedSegments - rotatedSegments

  //       // 旋转轮盘
  //       for (let i = 0; i < segmentsToAdd; i++) {
  //         currentSegment.value = (currentSegment.value + 1) % SEGMENT_COUNT
  //         // 播放声音
  //         play('roleta', 0.8) 
  //       }

  //       // 更新已旋转的段数
  //       rotatedSegments = newRotatedSegments
  //     }

  //     // 确保在最后一帧精确到达目标位置
  //     if (progress >= 0.999) {
  //       currentSegment.value = targetSegment
  //       console.log(`动画结束，最终位置: ${currentSegment.value}`)
  //     }
  //   },
  //   onComplete: () => {
  //     // 双重检查，确保确实停在了目标位置
  //     if (currentSegment.value !== targetSegment) {
  //       console.error(`动画完成但位置不匹配! 当前: ${currentSegment.value}, 目标: ${targetSegment}`);
  //       currentSegment.value = targetSegment;
  //     }

  //     console.log(`动画完成，高亮位置: ${currentSegment.value}`)
  //     isAnimating.value = false
  //     // 开始闪烁效果 - 延迟一小段时间让动画停止更自然
  //     setTimeout(() => {
  //       blinkAndRemoveHighlight()
  //     }, 200)
  //   }
  // })
}

// 停止动画
// const stopAnimation = () => {
//   gsap.killTweensOf("*")
//   isAnimating.value = false
//   console.log('动画已手动停止')
// }

// 闪烁并移除高光
const blinkAndRemoveHighlight = () => {
  if (!currentCard.value) return

  // 验证当前位置和目标牌的匹配
  const targetSegment = RANK_TO_SEGMENT[currentCard.value.rank]
  if (currentSegment.value !== targetSegment) {
    console.error(`闪烁开始前位置不匹配! 当前: ${currentSegment.value}, 目标: ${targetSegment}`);
    // 强制修正位置
    currentSegment.value = targetSegment;
  }

  // 设置花色和大小高亮
  highlightSuit.value = SUIT_TO_AREA[currentCard.value.suit]
  highlightSize.value = getSizeArea(currentCard.value.rank)

  console.log('-------- 高亮区域 --------')
  console.log(`花色区域: ${highlightSuit.value || '无'}`)
  console.log(`大小区域: ${highlightSize.value ? (highlightSize.value === 'Large' ? '大' : '小') : '无'}`)
  console.log('--------------------------')

  // 使用GSAP创建闪烁效果
  const tl = gsap.timeline({
    onComplete: () => {
      showHighlight.value = false
      highlightSuit.value = null
      highlightSize.value = null
      currentSegment.value = -1
      clearAllBetAmounts()
      console.log('闪烁结束，高亮已移除，筹码移除')
    }
  })

  // 创建3次闪烁 - 使时间间隔更自然
  for (let i = 0; i < 5; i++) {
    tl.to(showHighlight, {
      value: false,
      duration: 0.15,
      immediateRender: false,
      onStart: () => {
        if (i === 0) console.log(`开始闪烁: 第${i + 1}次`)
      }
    })
      .to(showHighlight, {
        value: true,
        duration: 0.15,
        immediateRender: false,
        onComplete: () => {
          console.log(`闪烁: 第${i + 1}次完成`)
        }
      })
  }
}
const totalBetAmount = computed(() => {
  let total = 0
  betAmounts.value.forEach((amount) => {
    total += amount
  })
  return total
})
watch(() => gameStore.phase, (phase) => {
  if (phase === 'finished') {
    console.log('✅ phase 是 finished，准备开始动画')
    startAnimation()
  }
})

/**
 * 处理清空投注金额事件
 * 当收到 FinishedAsync 或 OpeningAsync 信号时触发
 */
const handleClearBetAmounts = (event: CustomEvent) => {
  const data = event.detail
  const reason = data.reason || 'unknown'

  console.log(`🧹 [LightWheel] 收到清空投注金额事件 (${reason}):`, data)

  // 清空所有投注金额显示
  clearAllBetAmounts()

  if (reason === 'opening') {
    console.log('🆕 [LightWheel] 新一期开始，投注金额已清空')
  } else if (reason === 'finished') {
    console.log('🎯 [LightWheel] 开奖结束，投注金额已清空')
  }
}

/**
 * 测试函数：手动添加投注金额到所有区域
 */
const testBetAmounts = () => {
  console.log('🧪 [LightWheel] 测试：添加投注金额到所有区域')

  // 测试数字区域 (A=0, 2=1, ..., K=12)
  for (let i = 0; i <= 12; i++) {
    const key = `rank-${i}`
    betAmounts.value.set(key, 10)
    console.log(`🧪 [LightWheel] 添加测试投注: ${key} = 10`)
  }

  // 测试花色区域
  const suits = ['black', 'heart', 'club', 'diamond']
  suits.forEach(suit => {
    const key = `suit-${suit}`
    betAmounts.value.set(key, 20)
    console.log(`🧪 [LightWheel] 添加测试投注: ${key} = 20`)
  })

  // 测试大小区域
  betAmounts.value.set('size-Large', 30)
  betAmounts.value.set('size-Small', 30)
  console.log('🧪 [LightWheel] 添加测试投注: size-Large = 30, size-Small = 30')

  console.log('🧪 [LightWheel] 当前所有投注金额:', Array.from(betAmounts.value.entries()))
}

// 组件挂载时注册事件监听器
onMounted(() => {
  console.log('🔧 [LightWheel] 注册事件监听器')
  window.addEventListener('lotteryOrderSuccess', handleBetSuccess as EventListener)
  window.addEventListener('clearBetAmounts', handleClearBetAmounts as EventListener)

  // 暴露测试函数到全局，方便调试
  ;(window as any).testBetAmounts = testBetAmounts
  console.log('🧪 [LightWheel] 测试函数已暴露到全局: window.testBetAmounts()')
})

// 组件卸载时清理
onUnmounted(() => {
  console.log('🧹 [LightWheel] 清理事件监听器和动画')
  // 移除事件监听器
  window.removeEventListener('lotteryOrderSuccess', handleBetSuccess as EventListener)
  window.removeEventListener('clearBetAmounts', handleClearBetAmounts as EventListener)

  // 清理动画
  gsap.killTweensOf("*")
  isAnimating.value = false

  // 清除任何可能存在的高亮定时器
  if (highlightTimerId !== null) {
    clearTimeout(highlightTimerId)
    highlightTimerId = null
  }
  console.log('组件卸载，动画已清理')
})
</script>
<template>
  <!-- 测试当前牌显示 -->
  <!-- <div class="card-display fixed" v-if="currentCard">
    <div class="poker flex bg-white rounded-[4px] px-2  gap-0  items-center justify-center"  :class="cardColor">
      <span class="text-[20px]"> {{ SUIT_SYMBOLS[currentCard.suit] }}</span>
      <span>{{ currentCard.rank }}</span>
    </div>
  </div> -->
  <div class="wheel-container relative">
    <svg class="game" viewBox="0 0 473 473" fill="none" xmlns="http://www.w3.org/2000/svg">
      <!-- 定义滤镜 - 精确的内阴影效果 -->
      <defs>
        <!-- 内部阴影光效滤镜 -->
        <filter id="filter0_i" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha" />
          <feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_innerShadow" />
          <feOffset />
          <feGaussianBlur stdDeviation="15" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.133333 0 0 0 0 0.133333 0 0 0 1 0" />
          <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
        </filter>
        <!-- 背景渐变边框-->
        <linearGradient id=":r1:-paint0_linear_1_2" x1="343.5" y1="278" x2="32.5" y2="278"
          gradientUnits="userSpaceOnUse">
          <stop stop-color="white" stop-opacity="0.62"></stop>
          <stop offset="1" stop-color="white" stop-opacity="0.45"></stop>
        </linearGradient>


        <!-- 外光阴影 -->
        <linearGradient id=":r1:-paint1_linear_1_2" x1="188" y1="25.5" x2="188" y2="542.35"
          gradientUnits="userSpaceOnUse">
          <stop stop-color="#D4FFEA"></stop>
          <stop offset="0.503472" stop-color="#71A9A2"></stop>
          <stop offset="1" stop-color="#D4FFEA"></stop>
        </linearGradient>

        <!-- 径向渐变 -->
        <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
          gradientTransform="translate(236.5 236.5) rotate(90) scale(167.5)">
          <stop offset="0.425" stop-color="white" stop-opacity="0" />
          <stop offset="0.76" stop-color="white" stop-opacity="0.22" />
          <stop offset="0.865" stop-color="#EEEEEE" stop-opacity="0.5" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </radialGradient>
        <!-- 赔率1渐变 -->
        <linearGradient id="paint0_linear_1366_23828" x1="26.9995" y1="17.9999" x2="26.9995" y2="4.49988"
          gradientUnits="userSpaceOnUse">
          <stop stop-color="#B62B27" />
          <stop offset="1" stop-color="#F98647" />
        </linearGradient>
      </defs>

      <!-- 背景圆形 -->
      <circle cx="236.5" cy="236.5" r="177.5" fill="url(#:r1:-paint0_linear_1_2)" fill-opacity="0.18" />
      <circle cx="236.5" cy="236.5" r="178.5" stroke="#7C7C7C" stroke-opacity="0.5" stroke-width="1.62827" />
      <circle cx="236.5" cy="236.5" r="167.5" fill="#0F3D5F" />

      <!-- 赔率1 -->
      <g transform="translate(208.8, 55.8) rotate(0)">
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M5.91083 3.19189L0.46875 19.0103C8.95162 17.6867 17.6453 16.9997 26.4995 16.9997C36.4642 16.9997 46.2256 17.8699 55.712 19.5383L50.0883 3.19189C49.5327 1.57717 48.0134 0.493164 46.3058 0.493164H9.69324C7.98564 0.493164 6.46634 1.57718 5.91083 3.19189Z"
          fill="url(#paint0_linear_1366_23828)" />
        <text x="27.62" y="10" fill="white" font-size="10" font-weight="400" text-anchor="middle">x12</text>
      </g>

      <!-- 分段 -->
      <g id="segments" fill="url(#:r1:-paint1_linear_1_2)">
        <!-- 分段1 - A -->
        <g :filter="showHighlight && currentSegment === 0 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 0, event)">
          <path id="segment-0"
            d="M158.615 88.1413C182.619 75.5429 209.322 68.9591 236.432 68.9552L236.44 129.255C219.09 129.258 202 133.471 186.637 141.534L158.615 88.1413Z"
            fill="#D5A034" fill-opacity="0.95" />
          <text x="200" y="105" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">A</text>
          <g v-if="getBetAmount('rank', 0)">
            <rect x="185" y="110" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="205" y="125" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 0) }}</text>
          </g>
        </g>

        <!-- 分段2 -->
        <g :filter="showHighlight && currentSegment === 1 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 1, event)">
          <path id="segment-1"
            d="M236.456 68.9561C263.565 68.9561 290.27 75.536 314.276 88.1309L286.261 141.528C270.897 133.467 253.806 129.256 236.456 129.256V68.9561Z"
            fill="#E5CD4E" fill-opacity="0.95" />
          <text x="270" y="105" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">2</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 1)">
            <rect x="245" y="110" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="265" y="125" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 1) }}</text>
          </g>
        </g>

        <!-- 分段3 -->
        <g :filter="showHighlight && currentSegment === 2 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 2, event)">
          <path id="segment-2"
            d="M314.265 88.1257C338.272 100.719 358.864 118.951 374.271 141.256L324.658 175.528C314.797 161.253 301.618 149.584 286.254 141.525L314.265 88.1257Z"
            fill="#98BF70" fill-opacity="0.95" />
          <text x="330" y="135" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">3</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 2)">
            <rect x="300" y="140" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="320" y="155" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 2) }}</text>
          </g>
        </g>

        <!-- 分段4 -->
        <g :filter="showHighlight && currentSegment === 3 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 3, event)">
          <path id="segment-3"
            d="M374.265 141.245C389.674 163.549 399.44 189.26 402.723 216.17L342.867 223.473C340.766 206.251 334.516 189.796 324.654 175.521L374.265 141.245Z"
            fill="#37A333" fill-opacity="0.95" />
          <text x="365" y="195" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">4</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 3)">
            <rect x="340" y="195" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="360" y="210" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 3) }}</text>
          </g>
        </g>

        <!-- 分段5 -->
        <g :filter="showHighlight && currentSegment === 4 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 4, event)">
          <path id="segment-4"
            d="M402.724 216.18C406.006 243.09 402.707 270.395 393.111 295.749L336.715 274.404C342.857 258.177 344.968 240.702 342.868 223.48L402.724 216.18Z"
            fill="#2EBABB" fill-opacity="0.95" />
          <text x="378" y="263" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">5</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 4)">
            <rect x="350" y="255" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="370" y="270" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 4) }}</text>
          </g>
        </g>



        <!-- 分段6 -->
        <g :filter="showHighlight && currentSegment === 5 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 5, event)">
          <path id="segment-5"
            d="M393.102 295.771C383.502 321.124 367.892 343.767 347.612 361.758L307.596 316.649C320.575 305.135 330.565 290.643 336.709 274.417L393.102 295.771Z"
            fill="#3182B9" fill-opacity="0.95" />
          <text x="355" y="325" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">6</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 5)">
            <rect x="330" y="300" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="350" y="315" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 5) }}</text>
          </g>
        </g>

        <!-- 分段7 -->
        <g :filter="showHighlight && currentSegment === 6 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 6, event)">
          <path id="segment-6"
            d="M347.594 361.774C327.312 379.761 302.967 392.557 276.649 399.062L262.18 340.524C279.023 336.36 294.604 328.171 307.584 316.659L347.594 361.774Z"
            fill="#7B99CD" fill-opacity="0.95" />
          <text x="303" y="372" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">7</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 6)">
            <rect x="280" y="350" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="300" y="365" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 6) }}</text>
          </g>
        </g>

        <!-- 分段8 -->
        <g :filter="showHighlight && currentSegment === 7 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 7, event)">
          <path id="segment-7"
            d="M276.625 399.068C250.307 405.569 222.804 405.586 196.478 399.115L210.87 340.558C227.719 344.699 245.321 344.689 262.164 340.528L276.625 399.068Z"
            fill="#793C8F" fill-opacity="0.95" />
          <text x="236" y="387" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">8</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 7)">
            <rect x="215" y="365" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="236" y="380" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 7) }}</text>
          </g>
        </g>

        <!-- 分段9 -->
        <g :filter="showHighlight && currentSegment === 8 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 8, event)">
          <path id="segment-8"
            d="M196.371 399.088C170.05 392.601 145.696 379.821 125.402 361.847L165.381 316.706C178.37 328.21 193.956 336.389 210.802 340.541L196.371 399.088Z"
            fill="#865FA0" fill-opacity="0.95" />
          <text x="170" y="373" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">9</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 8)">
            <rect x="150" y="350" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="170" y="365" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 8) }}</text>
          </g>
        </g>

        <!-- 分段10 -->
        <g :filter="showHighlight && currentSegment === 9 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 9, event)">
          <path id="segment-9"
            d="M125.383 361.831C105.092 343.854 89.4664 321.221 79.8497 295.875L136.228 274.484C142.383 290.705 152.383 305.191 165.37 316.696L125.383 361.831Z"
            fill="#CA3653" fill-opacity="0.95" />
          <text x="120" y="325" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">10</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 9)">
            <rect x="100" y="300" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="120" y="315" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 9) }}</text>
          </g>
        </g>

        <!-- 分段11 - J -->
        <g :filter="showHighlight && currentSegment === 10 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 10, event)">
          <path id="segment-10"
            d="M79.841 295.852C70.2279 270.504 66.9108 243.202 70.1745 216.289L130.036 223.549C127.947 240.773 130.07 258.246 136.222 274.469L79.841 295.852Z"
            fill="#D7776C" fill-opacity="0.95" />
          <text x="95" y="260" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">J</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 10)">
            <rect x="80" y="260" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="100" y="275" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 10) }}</text>
          </g>
        </g>

        <!-- 分段12 - Q -->
        <g :filter="showHighlight && currentSegment === 11 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 11, event)">
          <path id="segment-11"
            d="M70.1899 216.265C73.4576 189.353 83.2084 163.637 98.6051 141.324L148.236 175.571C138.382 189.852 132.142 206.31 130.05 223.534L70.1899 216.265Z"
            fill="#DD9146" fill-opacity="0.95" />
          <text x="100" y="195" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">Q</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 11)">
            <rect x="90" y="200" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="110" y="215" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 11) }}</text>
          </g>
        </g>

        <!-- 分段13 - K -->
        <g :filter="showHighlight && currentSegment === 12 ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('rank', 12, event)">
          <path id="segment-12"
            d="M98.6059 141.305C114.006 118.994 134.591 100.755 158.593 88.1527L186.624 141.542C171.262 149.607 158.088 161.28 148.232 175.559L98.6059 141.305Z"
            fill="#EBD77E" fill-opacity="0.95" />
          <text x="138" y="138" fill="black" fill-opacity="0.95" font-size="24" font-weight="bold"
            text-anchor="middle">K</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('rank', 12)">
            <rect x="120" y="120" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="140" y="135" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('rank', 12) }}</text>
          </g>
        </g>
      </g>


      <!-- 中心设计 -->
      <g transform="translate(93.5, 93.5)">
        <!-- 外部4个部分 - 黑、红、梅、方 -->
        <!-- 左侧部分 - 方块 -->
        <g :filter="highlightSuit === 'diamond' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('suit', 'diamond', event)">
          <path
            d="M99.8682 100.576C76.711 124.111 76.711 161.889 99.8682 185.424L71.5839 213.709C32.8065 174.552 32.8065 111.448 71.5839 72.2914L99.8682 100.576Z"
            fill="#3156A4" stroke="#587DCA" />
          <g transform="translate(60, 125)">
            <path
              d="M7.2017 1.16071e-06C6.50227 1.16071e-06 5.84426 0.330358 5.42749 0.891155L0.600854 7.09773C-0.200285 8.12031 -0.200285 9.87291 0.600854 10.9027L5.43919 17.1237C5.64641 17.3973 5.91445 17.6189 6.22212 17.771C6.52978 17.9231 6.86862 18.0015 7.21182 18C7.55502 17.9985 7.89317 17.9172 8.19951 17.7624C8.50584 17.6077 8.77196 17.3837 8.9768 17.1084L13.8025 10.9027C14.6037 9.88011 14.6037 8.12751 13.8025 7.09773L8.9642 0.876753C8.7584 0.60409 8.49206 0.382965 8.1862 0.230815C7.88034 0.0786655 7.54331 -0.000349296 7.2017 1.16071e-06Z"
              fill="#E1F6FF" />
            <text x="7" y="30" alignment-baseline="middle" fill="white" font-size="16" font-weight="500"
              text-anchor="middle">方</text>
            <!-- 添加投注金额显示 -->
            <g v-if="getBetAmount('suit', 'diamond')">
              <rect x="-15" y="45" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
              <text x="7" y="60" fill="white" font-size="14" font-weight="bold" text-anchor="middle">{{
                getBetAmount('suit', 'diamond') }}</text>
            </g>
          </g>
        </g>

        <!-- 下方部分 - 梅花 -->
        <g :filter="highlightSuit === 'club' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('suit', 'club', event)">
          <path
            d="M100.576 186.132C124.111 209.289 161.889 209.289 185.424 186.132L213.709 214.416C174.552 253.194 111.448 253.194 72.2914 214.416L100.576 186.132Z"
            fill="#3156A4" stroke="#587DCA" />
          <g transform="translate(124, 215)">
            <path
              d="M17.7301 10.567C17.7304 11.5468 17.3865 12.4955 16.7586 13.2477C16.1307 13.9998 15.2587 14.5076 14.2946 14.6823C13.3306 14.8571 12.3357 14.6878 11.4838 14.204C10.6318 13.7202 9.97678 12.9525 9.63302 12.035C9.62982 14.7865 9.80636 16.6173 12.4173 18H5.30863C7.9196 16.6177 8.09614 14.7873 8.09294 12.035C7.80568 12.8 7.30048 13.464 6.63984 13.945C5.9792 14.4259 5.19208 14.7026 4.37584 14.7409C3.55959 14.7792 2.75002 14.5774 2.04724 14.1605C1.34446 13.7436 0.779293 13.1298 0.421639 12.3951C0.063985 11.6604 -0.0704704 10.8369 0.0349004 10.0266C0.140271 9.2163 0.480846 8.45464 1.01451 7.83583C1.54817 7.21701 2.25151 6.76819 3.03754 6.54487C3.82358 6.32155 4.65783 6.33353 5.43714 6.57932C4.99851 5.95268 4.74016 5.21772 4.69023 4.45445C4.64029 3.69118 4.80068 2.92884 5.15392 2.25039C5.50717 1.57194 6.03975 1.00338 6.69369 0.606595C7.34763 0.20981 8.09788 0 8.86278 0C9.62768 0 10.3779 0.20981 11.0319 0.606595C11.6858 1.00338 12.2184 1.57194 12.5716 2.25039C12.9249 2.92884 13.0853 3.69118 13.0353 4.45445C12.9854 5.21772 12.7271 5.95268 12.2884 6.57932C12.915 6.38173 13.5792 6.33447 14.2274 6.44138C14.8756 6.54828 15.4896 6.80634 16.0195 7.19466C16.5494 7.58298 16.9804 8.09064 17.2776 8.67653C17.5748 9.26242 17.7298 9.91007 17.7301 10.567Z"
              fill="#E1F6FF" />
            <text x="28" y="15" fill="white" font-size="16" font-weight="500" text-anchor="middle">梅</text>
            <!-- 添加投注金额显示 -->
            <g v-if="getBetAmount('suit', 'club')">
              <rect x="-20" y="-10" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
              <text x="0" y="5" fill="white" font-size="14" font-weight="bold" text-anchor="middle">{{
                getBetAmount('suit', 'club') }}</text>
            </g>
          </g>
        </g>

        <!-- 上方部分 - 黑桃 -->
        <g :filter="highlightSuit === 'black' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('suit', 'black', event)">
          <path
            d="M185.424 99.8682C161.889 76.711 124.111 76.711 100.576 99.8682L72.2914 71.5839C111.448 32.8065 174.552 32.8065 213.709 71.5839L185.424 99.8682Z"
            fill="#3156A4" stroke="#587DCA" />

          <g transform="translate(124, 55)">
            <text x="28" y="15" fill="white" font-size="16" font-weight="500" text-anchor="middle">黑</text>
            <path
              d="M16.2487 11.0702C16.2487 13.5778 14.0867 14.7265 12.5916 14.7273C10.9244 14.7273 9.66483 13.9722 8.91492 13.3556C9.01381 15.3955 9.49667 16.8529 11.6635 18H4.58518C6.75205 16.8529 7.23491 15.3955 7.33381 13.3556C6.58389 13.9718 5.32389 14.7281 3.65709 14.7273C2.16407 14.7273 0 13.5778 0 11.0702C0 6.487 3.22869 7.73419 8.12416 0C13.02 7.73419 16.2487 6.4874 16.2487 11.0702Z"
              fill="#E1F6FF" />
            <!-- 添加投注金额显示 -->
            <g v-if="getBetAmount('suit', 'black')">
              <rect x="35" y="12" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
              <text x="55" y="28" fill="white" font-size="14" font-weight="bold" text-anchor="middle">{{
                getBetAmount('suit', 'black') }}</text>
            </g>
          </g>
        </g>

        <!-- 右侧部分 - 红桃 -->
        <g :filter="highlightSuit === 'heart' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('suit', 'heart', event)">
          <path
            d="M186.131 185.424C209.288 161.889 209.288 124.111 186.131 100.576L214.415 72.2914C253.193 111.448 253.193 174.552 214.415 213.709L186.131 185.424Z"
            fill="#3156A4" stroke="#587DCA" />
          <g transform="translate(215, 125)">
            <path
              d="M9 16.515L7.695 15.327C3.06 11.124 0 8.343 0 4.95C0 2.169 2.178 0 4.95 0C6.516 0 8.019 0.729 9 1.872C9.981 0.729 11.484 0 13.05 0C15.822 0 18 2.169 18 4.95C18 8.343 14.94 11.124 10.305 15.327L9 16.515Z"
              fill="#E1F6FF" />
            <text x="9" y="32" fill="white" font-size="16" font-weight="500" text-anchor="middle">红</text>
            <!-- 添加投注金额显示 -->
            <g v-if="getBetAmount('suit', 'heart')">
              <rect x="-20" y="48" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
              <text x="2" y="62" fill="white" font-size="14" font-weight="bold" text-anchor="middle">{{
                getBetAmount('suit', 'heart') }}</text>
            </g>
          </g>
        </g>


        <!-- 内部2个部分 - 添加文字：大、小 -->
        <!-- 左半圆 - 小 -->
        <g :filter="highlightSize === 'Small' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('size', 'Small', event)">
          <path
            d="M142.5 202.496C109.869 202.228 83.5 175.694 83.5 143C83.5 110.306 109.869 83.7715 142.5 83.5029V202.496Z"
            fill="#3156A4" stroke="#587DCA" />
          <text x="115" y="140" fill="white" font-size="24" font-weight="bold" text-anchor="middle">{{ sizeGameData.small?.displayName || '小' }}</text>
          <text x="115" y="160" fill="white" font-size="14" font-weight="400" text-anchor="middle">A-6</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('size', 'Small')">
            <rect x="95" y="160" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="115" y="175" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('size', 'Small') }}</text>
          </g>
        </g>

        <!-- 右半圆 - 大 -->
        <g :filter="highlightSize === 'Large' && showHighlight ? 'url(#filter0_i)' : ''"
          @click.stop="(event) => handleClick('size', 'Large', event)">
          <path
            d="M143.5 83.5039C176.131 83.7725 202.5 110.306 202.5 143C202.5 175.694 176.131 202.228 143.5 202.497V83.5039Z"
            fill="#3156A4" stroke="#587DCA" />
          <text x="170" y="140" fill="white" font-size="24" font-weight="bold" text-anchor="middle">{{ sizeGameData.large?.displayName || '大' }}</text>
          <text x="170" y="160" fill="white" font-size="14" font-weight="400" text-anchor="middle">8-K</text>
          <!-- 添加投注金额显示 -->
          <g v-if="getBetAmount('size', 'Large')">
            <rect x="150" y="160" width="42" height="18" rx="4" fill="rgba(0,0,0,0.5)" />
            <text x="170" y="175" fill="white" font-size="16" font-weight="bold" text-anchor="middle">{{
              getBetAmount('size', 'Large') }}</text>
          </g>
        </g>
      </g>

      <!-- 光圈 -->
      <g style="pointer-events: none;">
        <path
          d="M404 236.5C404 329.008 329.008 404 236.5 404C143.992 404 69 329.008 69 236.5C69 143.992 143.992 69 236.5 69C329.008 69 404 143.992 404 236.5ZM129.3 236.5C129.3 295.705 177.295 343.7 236.5 343.7C295.705 343.7 343.7 295.705 343.7 236.5C343.7 177.295 295.705 129.3 236.5 129.3C177.295 129.3 129.3 177.295 129.3 236.5Z"
          fill="url(#paint0_radial)" />
      </g>
      <!-- 赔率中 -->
      <g transform="translate(213.5, 129) rotate(0)">
        <path
          d="M0.688477 3.15137C15.3673 -0.249524 30.6499 -0.377319 45.374 2.76855L41.8564 12.8994C41.2275 14.709 39.5221 15.9227 37.6064 15.9229H8.3252C6.52893 15.9229 4.91674 14.856 4.2041 13.2314L4.07422 12.8994L0.688477 3.15137Z"
          fill="#2F3D86" stroke="#587DCA" pointer-events="none" />
        <text x="23" y="11" fill="white" font-size="10" font-weight="400" text-anchor="middle">x3.75</text>
      </g>
      <!-- 赔率内 -->
      <g transform="translate(216.5, 176) rotate(0)">
        <path
          d="M20.9287 0.5C27.4066 0.5 33.6357 1.56132 39.4541 3.51465L36.5322 12.3408C35.9222 14.1827 34.2008 15.4268 32.2607 15.4268H7.70996C5.89105 15.4268 4.26418 14.3333 3.56543 12.6787L3.43848 12.3408L0.711914 4.11133C6.80688 1.8521 13.3833 0.582718 20.2471 0.503906L20.9287 0.5Z"
          fill="#2F3D86" stroke="#587DCA" />
        <text x="20" y="11" fill="white" font-size="10" font-weight="400" text-anchor="middle">x1.95</text>
      </g>

    </svg>
    <div class="flex justify-end items-end self-end mt-[-40px]">
      <div class="flex justify-between items-center gap-2 text-white px-4">
        <span>总投注</span>
      <span>{{ totalBetAmount }}</span>
      </div>
      
    </div>
    <ChatInfo />
    <TabBar @clear="clearAllBetAmounts" />

    <!-- 控制按钮 -->
    <!-- <div v-if="isDev" class="controls">
      <button class="start-btn" @click="startAnimation" :disabled="isAnimating">调试旋转</button>
    </div> -->


  </div>
</template>

<style scoped>
.wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.controls {
  display: flex;
  gap: 10px;
  margin-top: -50px;
}

button {
  padding: 10px 20px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.start-btn {
  background-color: #3156A4;
}

.start-btn:disabled {
  background-color: #CCCCCC;
}

.stop-btn {
  background-color: #6E84B4;
}

.stop-btn:disabled {
  background-color: #CCCCCC;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  transform: scale(1.05);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

path {
  transition: all 0.3s ease;
  cursor: pointer;
}

/* .card-display {
  margin-top: 20px;
  text-align: center;
  font-size: 18px;
}

.card-display h3 {
  margin-bottom: 10px;
} */

/* .card {
  display: flex;
  flex-direction: column;
  padding: 8px 16px;
  background-color: white;
  border-radius: 5px;
  font-size: 28px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
} */

.poker.red {
  color: red;
}

.poker.black {
  color: black;
}

.game {
  width: 473px;
  height: 473px;
}

@media (min-width: 768px) {
  .game {
    width: 573px;
    height: 573px;
  }
}

@media (min-width: 1280px) {
  .game {
    width: 673px;
    height: 673px;
  }
}
</style>
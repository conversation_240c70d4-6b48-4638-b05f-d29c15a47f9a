import type { App } from 'vue'
import { inject } from 'vue'
import WebApp from '@twa-dev/sdk'
import type { WebApp as TwaWebApp } from '@twa-dev/types'

const TWA_KEY = Symbol('TWA')

// ✅ 保证即使不是在 Telegram 也能用
const instance: TwaWebApp = (() => {
  if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
    WebApp.ready()
    WebApp.isClosingConfirmationEnabled = true
    return WebApp as TwaWebApp
  } else {
    console.warn('[TWA] Not running in Telegram. Using mock WebApp.')
    return {
      HapticFeedback: {
        notificationOccurred: (type: string) => console.log(`[Mock Haptic] ${type}`),
      },
      showAlert: (msg: string) => alert(`[Mock Alert] ${msg}`),
      close: () => console.log('[Mock close()]'),
      sendData: (data: string) => console.log(`[Mock sendData] ${data}`),
      initData: '',
      initDataUnsafe: {} as any,
      isExpanded: false,
      isClosingConfirmationEnabled: false,
      expand: () => {},
      enableClosingConfirmation: () => {},
      disableClosingConfirmation: () => {},
      onEvent: () => {},
      offEvent: () => {},
    } as unknown as TwaWebApp
  }
})()

// ✅ 插件注册：提供实例
export default {
  install(app: App) {
    app.provide(TWA_KEY, instance)
  },
}

// ✅ 组合式 API：组件内直接用，永远不会报错
export function useWebApp(): TwaWebApp {
  const twa = inject<TwaWebApp>(TWA_KEY)
  return twa || instance // fallback 兜底（理论上不会执行）
}

export { TWA_KEY }

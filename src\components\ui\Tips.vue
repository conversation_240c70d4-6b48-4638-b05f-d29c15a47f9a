<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'

const props = defineProps<{
  message: string
  type?: 'info' | 'success' | 'error' | 'warning'
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', val: boolean): void
}>()

// 背景统一为白色 50% 透明
const bgClass = 'bg-white/10 text-white text-[12px]'

watchEffect(() => {
  if (props.visible) {
    setTimeout(() => {
      emit('update:visible', false)
    }, 103000)
  }
})
</script>

<template>
  <transition name="fade-slide">
    <div
      v-if="visible"
      class="fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-[1500]"
      :class="bgClass"
    >
      {{ message }}
    </div>
  </transition>
</template>

<style scoped>
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translate(-50%, -20px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translate(-50%, -20px);
}
</style>

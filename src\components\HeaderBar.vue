<template>
  <header class="fixed top-0 left-0 w-full h-[60px] flex items-center justify-between px-4 z-40">
    <!-- 左边：头像 + 标题 -->
    <!-- <div class="flex justify-center items-center space-x-3">
      <div class="avatar">
        <img src="https://t.me/i/userpic/320/1Uu2gnX9NgpUcv_LFwOn-S0aZEZWxweQg5k34N_eWIqRSm8Z63ITOZQDG6RXujDt.svg"
          style="width:35px;border-radius:10px;border:2px solid #e8c6f23d">
      </div>
      <div class="flex flex-col items-start leading-tight">
        <span class="text-white text-sm font-bold">惊喜</span>
        <span class="text-gray-400 text-xs">TGID 815566555</span>
      </div>
    </div> -->

    <!-- 余额 -->
     <div class="relative flex items-center justify-center gap-2">
      <div class="_container gap-1 flex items-center justify-center">
        <div class="_sit_number flex items-center justify-center">
          <img src="../assets/usdt.svg" alt="USDT" class="w-4 h-4" />
        </div>
        <div class="_sit_label flex items-center justify-center">50000.00</div>
      </div>
      <!-- 返奖后提示的返奖金额 -->
      <div class="absolute bottom-[-10px] right-0 text-[#04ff00] text-sm font-bold">+500</div>
     </div>
    
   


    <!-- 右边：设置按钮 -->
    <div class="relative flex items-center justify-center gap-2">
      <Button icon="#icon-Hourglass" fill="#fff" @click="toggleHistoryModal" />
      <Button icon="#icon-Settings" fill="#fff" @click="toggleMenu" />
    </div>
  </header>
  <!-- 提示 -->
   <div class="flex justify-center items-center my-4">
    <div class="border border-2 border-solid border-white/10 rounded-2xl p-2">提示内容</div>
   </div>
  <CustomMenu v-model:visible="showMenu" />
  <DrawHistoryModal v-model:visible="showHistory" :records="historyList" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Button from './ui/Button.vue'
import CustomMenu from './ui/CustomMenu.vue'
import DrawHistoryModal from './ui/DrawHistoryModal.vue'
import { useSoundBuffer } from '../composables/useSoundBuffer'

// 状态
const showHistory = ref(false)
const showMenu = ref(false)
const historyList = ref([
  { id: '669955', num: 'A', suit: 'c', size: '小' },
  { id: '669954', num: '10', suit: 's', size: '大' },
  { id: '669954', num: 'J', suit: 'h', size: '大' },
  { id: '669953', num: '8', suit: 'd', size: '小' }
])

// 声音播放
const { play } = useSoundBuffer()

// 切换历史记录弹窗
const toggleHistoryModal = () => {
  // 如果菜单已打开，先关闭
  if (showMenu.value) showMenu.value = false
  play('clear', 0.8)
  showHistory.value = !showHistory.value
  console.log('History visibility:', showHistory.value)
}

// 切换设置菜单
const toggleMenu = () => {
  // 如果历史记录已打开，先关闭
  if (showHistory.value) showHistory.value = false
  play('clear', 0.8)
  showMenu.value = !showMenu.value
  console.log('Menu visibility:', showMenu.value)
}
</script>


<style scoped>
._container {
  border: .0625em dashed rgba(125, 146, 255, .35);
  box-sizing: content-box;
  border-radius: .63em;
  padding: .16em .62em;
  height: 1.88em;
  overflow: hidden;
  display: flex;
  justify-content: center;
  --current-color: white;
}
</style>
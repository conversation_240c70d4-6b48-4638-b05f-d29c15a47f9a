// ========== 类型导入 ==========
import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { BACKEND_ERROR_CODE, createFlatRequest, HttpResponse, RequestOption } from '../utils/http-client';
import { getAuthorization, handleExpiredRequest, showErrorMsg } from './shared';
import { RequestInstanceState } from './type';
import { ErrorHandler } from '@/scripts/services/errorHandler';

// ========== Feivoo 请求头配置 ==========
/**
 * Feivoo 扑克游戏的请求头配置
 * x-source-id: 标识请求来源为 feivoo-poker
 * x-wrap-result: 标识是否需要包装返回结果
 */
const feivooHanders = {
  'x-source-id': 'feivoo-poker',
  'x-wrap-result': 'true',
};

// ========== 请求前处理函数 ==========
/**
 * 在发送请求前执行的处理函数
 * 主要功能：添加认证token到请求头
 * @param config Axios请求配置对象
 * @returns 处理后的请求配置
 */
async function onRequest(config: InternalAxiosRequestConfig) {
  // 获取当前的认证token
  const authorization = await getAuthorization();

  // 如果token存在，则添加到请求头中
  if (authorization !== null) {
    Object.assign(config.headers, { authorization });
  }

  return config;
}

// ========== 后端成功判断函数 ==========
/**
 * 判断后端请求是否成功
 * @param response Axios响应对象
 * @returns 是否成功的布尔值
 */
function isBackendSuccess(response: AxiosResponse) {
  // 检查响应头中的包装结果标识
  const wrapResult = response.headers['x-wrap-result'];
  
  // 如果启用了结果包装或响应数据包含success字段，则根据success字段判断
  if ((wrapResult && wrapResult.toLowerCase() === 'true') || response.data.success !== undefined) {
    return response.data.success;
  }
  
  // 否则根据HTTP状态码判断（200-299为成功）
  return response.status >= 200 && response.status < 300;
}

// ========== 后端响应转换函数 ==========
/**
 * 转换后端响应数据格式
 * @param response Axios响应对象
 * @returns 转换后的数据
 */
function transformBackendResponse(response: AxiosResponse<HttpResponse<unknown>, any>): any | Promise<any> {
  // 检查响应头中的包装结果标识
  const wrapResult = response.headers['x-wrap-result'];
  
  // 如果启用了结果包装或响应数据包含success字段，则返回result字段的数据
  if ((wrapResult && wrapResult.toLowerCase() === 'true') || response.data.success !== undefined) {
    return response.data.result;
  }
  
  // 否则直接返回原始响应数据
  return response.data;
}

// 🔥 声明一个变量来存储 request 实例，避免在函数中直接引用
let requestInstance: any = null;

// ========== 后端失败处理函数 ==========
/**
 * 处理后端请求失败的情况
 * 包含登出处理、token过期处理等逻辑
 * @param response 失败的响应对象
 * @param instance Axios实例
 * @returns 处理结果或重试的Promise
 */
async function onBackendFail(response: AxiosResponse<HttpResponse<unknown, any>, any>, instance: AxiosInstance) {
  const responseCode = response.data.error.code;

  // ========== 登出处理函数 ==========
  /**
   * 执行用户登出操作
   */
  async function handleLogout() {
    // 使用动态导入避免循环依赖
    const { useAuthStore } = await import('@/stores/auth');
    const { signOutAsync } = useAuthStore();
    await signOutAsync();
  }

  /**
   * 登出并清理相关状态
   */
  function logoutAndCleanup() {
    handleLogout();
    // 移除页面刷新前的事件监听器
    window.removeEventListener('beforeunload', handleLogout);

    // 🔥 使用 requestInstance 而不是 request
    if (requestInstance?.state?.errMsgStack) {
      requestInstance.state.errMsgStack = requestInstance.state.errMsgStack.filter(
        (msg: string) => msg !== response.data.error.message
      );
    }
  }

  // ========== 直接登出码处理 ==========
  /**
   * 当后端响应码在logoutCodes中时，直接登出用户并重定向到登录页
   */
  const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
  if (logoutCodes.includes(responseCode)) {
    handleLogout();
    return null;
  }

  // ========== 模态框登出码处理 ==========
  /**
   * 当后端响应码在modalLogoutCodes中时，通过显示模态框的方式登出用户
   */
  const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
  if (
    modalLogoutCodes.includes(responseCode) &&
    !requestInstance?.state?.errMsgStack?.includes(response.data.error.message)
  ) {
    // 将错误消息添加到堆栈中，避免重复显示
    if (requestInstance?.state) {
      requestInstance.state.errMsgStack = [...(requestInstance.state.errMsgStack || []), response.data.error.message];
    }

    // 阻止用户刷新页面，确保在登出前不会丢失状态
    window.addEventListener('beforeunload', handleLogout);

    return null;
  }

  // ========== Token过期处理 ==========
  /**
   * 当后端响应码在expiredTokenCodes中时，表示token已过期，需要刷新token
   * 注意：refreshToken API 不能返回expiredTokenCodes中的错误码，否则会形成死循环
   * 应该返回logoutCodes或modalLogoutCodes中的错误码
   */
  const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
  if (expiredTokenCodes.includes(responseCode)) {
    // 尝试处理过期的请求（刷新token）
    const success = await handleExpiredRequest(requestInstance?.state || {} as RequestInstanceState);
    
    if (success) {
      // 刷新成功，获取新的认证token并重新发送请求
      const authorization = await getAuthorization();
      Object.assign(response.config.headers, { authorization });
      return instance.request(response.config) as Promise<AxiosResponse>;
    }
  }

  return null;
}

// ========== 错误处理函数 ==========
/**
 * 处理HTTP请求过程中发生的错误
 * @param error Axios错误对象
 */
function onError(error: AxiosError<HttpResponse<unknown, any>>): void | Promise<void> {
  // 使用专用的错误处理器处理错误，获取错误详情
  const errorDetail = ErrorHandler.handle(error);
  
  let message = errorDetail.message;
  let backendErrorCode = '';

  // ========== 后端错误信息提取 ==========
  /**
   * 如果是后端返回的错误，提取具体的错误信息和错误码
   */
  if (error.code === BACKEND_ERROR_CODE) {
    message = error.response?.data.error.message || message;
    backendErrorCode = error.response?.data?.error.code || '';
  }

  // ========== 认证错误特殊处理 ==========
  /**
   * 如果错误码属于需要登出的错误码，直接执行登出操作
   */
  const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
  if (logoutCodes.includes(backendErrorCode)) {
    // 使用动态导入避免循环依赖
    import('@/stores/auth').then(({ useAuthStore }) => {
      const { signOutAsync } = useAuthStore();
      signOutAsync();
    });
    return;
  }

  // ========== 显示错误消息 ==========
  /**
   * 向用户显示错误消息
   */
  if (requestInstance?.state) {
    showErrorMsg(requestInstance.state, message);
  }
}

// ========== 请求配置选项 ==========
/**
 * HTTP 请求的配置选项
 * 包含请求前处理、成功判断、失败处理、响应转换和错误处理
 */
const options = {
  onRequest,                    // 请求发送前的处理函数
  isBackendSuccess,             // 判断后端请求是否成功的函数
  onBackendFail,                // 后端请求失败时的处理函数
  transformBackendResponse,     // 后端响应数据转换函数
  onError                       // 错误处理函数
} as RequestOption<HttpResponse>;

// ========== 创建请求实例 ==========
/**
 * 创建扁平化的HTTP请求实例
 * 使用环境变量中的基础URL和自定义请求头
 */
export const request = createFlatRequest<HttpResponse, RequestInstanceState>(
  {
    url: import.meta.env.VITE_BASE_URL,  // 从环境变量获取基础URL
    headers: feivooHanders               // 应用Feivoo特定的请求头
  },
  options
);

// 🔥 设置 requestInstance 引用
requestInstance = request;
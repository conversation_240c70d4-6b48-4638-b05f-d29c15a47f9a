<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSoundBuffer } from '../composables/useSoundBuffer'
import { useChipStore } from '../stores/chip'
import { useGameStore } from '@/stores/game'

const gameStore = useGameStore()
const chipStore = useChipStore()
const activeIndex = ref(0)
const currentChipGroupIndex = ref(0)

// 音效
const { play } = useSoundBuffer()

// 筹码组
const chipGroups = [
  [{ value: 1 }, { value: 5 }, { value: 10 }, { value: 100 }],
  [{ value: 10 }, { value: 50 }, { value: 100 }, { value: 500 }],
  [{ value: 100 }, { value: 500 }, { value: 1000 }, { value: 5000 }],
]

// 当前筹码组
const currentChips = computed(() => chipGroups[currentChipGroupIndex.value])

// 切换筹码
const selectChip = (index: number) => {
  play('clear', 0.8)
  activeIndex.value = index
  chipStore.setChip(currentChips.value[index].value)
}

// 切换筹码组
const switchChipGroup = () => {
  play('fold', 0.8)
  currentChipGroupIndex.value = (currentChipGroupIndex.value + 1) % chipGroups.length
  activeIndex.value = 0
}

// 蒙版显示逻辑
const showMask = computed(() => {
  if (gameStore.phase === 'stoping') return true
  if (gameStore.phase === 'opening') return !gameStore.revealed
  return true
})

const emit = defineEmits<{
  (e: 'clear'): void
}>()

const handleClickClear = () => {
  play('clear', 0.8)
  emit('clear')
}
</script>

<template>
  <div class="fixed inset-x-0 bottom-0 bg-[#23262B] h-[60px] flex items-center justify-center px-4 z-50">
    <!-- 蒙版 -->
    <transition name="fade">
      <div
        v-if="showMask"
        class="absolute inset-0 bg-black/90 z-50 flex justify-center items-center"
      >
        <span class="text-white font-semibold">等待下一期开奖</span>
      </div>
    </transition>

    <!-- 左边：切换筹码 -->
    <div class="flex absolute left-5 justify-center items-center">
      <button class="h-10 w-10 flex justify-center items-center rounded-full button" @click="switchChipGroup">
        <img src="../assets/transform.svg" class="w-5 h-5" />
      </button>
    </div>

    <!-- 中间：筹码组 -->
    <div class="flex justify-center items-center gap-2">
      <div
        v-for="(chip, index) in currentChips"
        :key="index"
        @click="selectChip(index)"
        class="relative flex justify-center items-center w-[50px] h-[50px] cursor-pointer"
        :class="{
          'border-2 border-[#7d92ff] rounded-full': activeIndex === index
        }"
      >
        <img src="../assets/chip/chip_blue.svg" class="w-10 h-10" />
        <span
          class="absolute text-black font-bold"
          :class="{
            'text-sm': chip.value < 100,
            'text-[10px]': chip.value >= 100
          }"
        >
          {{ chip.value }}
        </span>
      </div>
    </div>

    <!-- 右边：清空按钮 -->
    <div class="flex absolute right-5 justify-center items-center">
      <button class="h-10 w-10 flex justify-center items-center rounded-full button" @click="handleClickClear">
        <img src="../assets/clear.svg" class="w-5 h-5" />
      </button>
    </div>
  </div>
</template>

<style scoped>
button {
  outline: revert;
  border: none;
  background: transparent;
  font: inherit;
  cursor: pointer;
}

.button {
  transition: all 0.2s ease-out;
  padding: 0.63rem;
  border-radius: 0.625rem;
  border: 1px solid rgba(125, 146, 255, 0.3);
  background: radial-gradient(42% 42% at 50.56% 100%, rgba(255, 238, 149, 0) 0%, rgba(255, 238, 149, 0) 100%);
  backdrop-filter: blur(2.5px);
  color: white;
}

.button:hover {
  background: radial-gradient(42% 42% at 50.56% 100%, rgba(125, 146, 255, 0.7) 0%, rgba(239, 107, 252, 0) 100%);
  border: 1px solid rgba(125, 146, 255, 0.6);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>

// ========== 导入依赖 ==========
import { useAuthStore } from '@/stores/auth'; // 认证状态管理
import type { RequestInstanceState } from './type'; // 请求实例状态类型定义

// ========== 获取认证头信息函数 ==========
/**
 * 获取HTTP请求的Authorization头信息
 * 用于在API请求中添加用户认证token
 * 
 * @returns {string | null} 返回格式化的认证头字符串，如果用户未认证则返回null
 * 
 * @example
 * // 返回格式: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * const auth = getAuthorization();
 * if (auth) {
 *   headers.authorization = auth;
 * }
 */
export function getAuthorization() {
  // 从认证状态管理中获取token和认证状态
  const { token, isAuthorized } = useAuthStore();
  
  // 如果用户未认证，直接返回null
  if (!isAuthorized) {
    return null;
  }
  
  // 构造标准的Authorization头格式: "TokenType AccessToken"
  // 例如: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  const authorization = `${token?.tokenType} ${token?.accessToken}`;
  return authorization;
}

// ========== 处理过期请求函数 ==========
/**
 * 处理token过期的请求，尝试刷新token
 * 使用单例模式确保同时只有一个刷新请求在进行，避免并发刷新导致的问题
 * 
 * @param {RequestInstanceState} state - 请求实例的状态对象
 * @returns {Promise<boolean>} 返回刷新是否成功的Promise
 * 
 * @description
 * 该函数的设计考虑了以下场景：
 * 1. 多个并发请求同时遇到token过期
 * 2. 避免重复发起刷新token请求
 * 3. 确保所有等待的请求都能获得刷新结果
 */
export async function handleExpiredRequest(state: RequestInstanceState) {
  // 检查是否已经有正在进行的刷新token请求
  if (!state.refreshTokenFn) {
    // 如果没有，则创建新的刷新token请求
    const { refreshTokenAsync } = useAuthStore();
    
    // 将刷新token的Promise保存到state中
    // 传入参数true表示这是一个自动刷新请求（而非用户主动刷新）
    state.refreshTokenFn = refreshTokenAsync(true);
  }
  
  // 等待刷新token完成，无论是新创建的还是已存在的请求
  const success = await state.refreshTokenFn;
  
  // 返回刷新是否成功
  return success;
}

// ========== 显示错误消息函数 ==========
/**
 * 显示错误消息给用户，并管理错误消息堆栈
 * 防止相同错误消息的重复显示，提升用户体验
 * 
 * @param {RequestInstanceState} state - 请求实例的状态对象
 * @param {string} message - 要显示的错误消息
 * 
 * @description
 * 错误消息管理策略：
 * 1. 维护一个错误消息堆栈，记录当前显示的所有错误
 * 2. 防止相同错误消息的重复显示
 * 3. 为将来的消息自动清理机制预留接口
 */
export function showErrorMsg(state: RequestInstanceState, message: string) {
  // 初始化错误消息堆栈（如果不存在）
  if (!state.errMsgStack?.length) {
    state.errMsgStack = [];
  }
  
  // 检查当前消息是否已经在显示中
  const isExist = state.errMsgStack.includes(message);
  
  // 如果消息不存在于堆栈中，则添加并显示
  if (!isExist) {
    // 将消息添加到错误堆栈中
    state.errMsgStack.push(message);
    
    // TODO: 实现实际的错误消息显示逻辑
    // 以下是预留的消息显示代码，可根据实际使用的UI库进行调整
    
    // 使用 Naive UI 的消息组件示例：
    // window.$message?.error(message, {
    //   onLeave: () => {
    //     // 消息消失时从堆栈中移除
    //     state.errMsgStack = state.errMsgStack.filter(msg => msg !== message);
    //     
    //     // 5秒后清空所有错误消息堆栈（可选的清理机制）
    //     setTimeout(() => {
    //       state.errMsgStack = [];
    //     }, 5000);
    //   }
    // });
    
    // 其他UI库的实现示例：
    
    // 使用 Element Plus：
    // ElMessage.error({
    //   message: message,
    //   onClose: () => {
    //     state.errMsgStack = state.errMsgStack.filter(msg => msg !== message);
    //   }
    // });
    
    // 使用 Ant Design Vue：
    // message.error(message);
    // // 需要手动管理清理逻辑
    
    // 使用自定义通知组件：
    // showNotification({
    //   type: 'error',
    //   message: message,
    //   onDismiss: () => {
    //     state.errMsgStack = state.errMsgStack.filter(msg => msg !== message);
    //   }
    // });
    
    console.error('API Error:', message); // 临时的控制台输出，便于调试
  }
}
<script setup lang="ts">
import { ref } from 'vue'
const items = ref<string[]>([])
function addItem(text: string) {
  items.value.push(text)
  if (items.value.length > 5) {
    items.value.shift()
  }
}

defineExpose({
  addItem,
})
</script>

<template>
  <div class="text-list-container">
    <TransitionGroup name="list" tag="div" class="flex flex-col space-y-0">
      <span
        v-for="(item, index) in items"
        :key="item + '-' + index"
        class="text-white text-xs block"
      >
        {{ item }}
      </span>
    </TransitionGroup>
  </div>
</template>

<style scoped>
/* 进入时的动画 */
.list-enter-from {
  opacity: 0;
  transform: translateY(10px);
}
.list-enter-active {
  transition: all 0.3s ease;
}
.list-enter-to {
  opacity: 1;
  transform: translateY(0);
}

/* 离开时的动画 */
.list-leave-from {
  opacity: 1;
  transform: translateY(0);
}
.list-leave-active {
  transition: all 0.3s ease;
  position: absolute;
}
.list-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 移动中的动画！关键！ */
.list-move {
  transition: transform 0.3s ease;
}

.text-list-container {
  width: 100%;
  overflow: hidden;
  position: relative;
}
</style>

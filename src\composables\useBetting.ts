// src/composables/useBetting.ts
import { request } from '@/scripts/request'
import { useChipStore } from '@/stores/chip'
import { useGameStore } from '@/stores/game'

/**
 * 投注相关的组合式函数
 */
export function useBetting() {
  const chipStore = useChipStore()
  const gameStore = useGameStore()
  
  /**
   * 根据投注类型和ID获取对应的游戏数据
   * @param type 投注类型
   * @param id 投注区域ID
   * @returns 游戏数据中的 instruction 字段
   */
  const getGameInstruction = (type: 'rank' | 'suit' | 'size', id: string | number): string => {
    if (type === 'rank') {
      // 牌面投注：0->A, 1->2, ..., 12->K
      const rankMap = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
      const displayName = rankMap[id as number]
      const gameData = gameStore.numberGameTypes.find(game => game.displayName === displayName)
      return gameData?.instruction || displayName
    } else if (type === 'suit') {
      // 花色投注映射
      const suitMap: Record<string, string> = {
        'black': 'PokerSpades',    // 黑桃
        'heart': 'PokerHearts',    // 红心
        'club': 'PokerClubs',      // 梅花
        'diamond': 'PokerDiamonds' // 方块
      }
      const playingTypeId = suitMap[id as string]
      const gameData = gameStore.allSuitGameTypes.find(game => game.playingTypeId === playingTypeId)
      return gameData?.instruction || String(id)
    } else if (type === 'size') {
      // 大小投注：Large/Small
      const gameData = gameStore.sizeGameTypes.find(game => game.playingTypeId === id)
      return gameData?.instruction || String(id)
    }
    return String(id)
  }
  
  /**
   * 提交投注
   * @param type 投注类型 ('rank' | 'suit' | 'size')
   * @param id 投注区域ID
   * @param amount 投注金额（可选，默认使用选中的筹码值）
   * @returns Promise<boolean> 投注是否成功
   */
  const submitBet = async (
    type: 'rank' | 'suit' | 'size',
    id: string | number,
    amount?: number
  ): Promise<boolean> => {
    try {
      // 获取投注金额
      const betAmount = amount || chipStore.selectedChip || 1

      // 获取游戏指令
      const instruction = getGameInstruction(type, id)

      // 构造请求数据 - 新格式：instruction + 空格 + 金额 + 空格 + USDT
      const requestData = `${instruction} ${betAmount} USDT`

      console.log(`🎰 提交投注: ${requestData} (期号: ${gameStore.issueNumber})`)

      // 发送投注请求 - 使用你封装的 request
      const { data, error } = await request({
        url: '/lottery/betting',  // 会通过 Vite 代理到 http://*************:5065/lottery/betting
        method: 'POST',
        data: requestData,
        headers: {
          'Content-Type': 'application/json'
        },
      })

      if (error) {
        console.error(`❌ 投注失败:`, error)
        return false
      }

      console.log(`✅ 投注成功: ${requestData}`, data)
      return true

    } catch (error) {
      console.error('❌ 投注请求异常:', error)
      return false
    }
  }

  return {
    submitBet,
    getGameInstruction
  }
}
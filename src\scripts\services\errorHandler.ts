// ========================================
// 错误处理服务模块
// services/errorHandler.ts
// ========================================

/**
 * 错误类型枚举
 * 定义系统中可能出现的各种错误类型，用于统一的错误分类和处理
 * 
 * @enum {string}
 * @description
 * 通过枚举的方式定义错误类型，便于错误的分类处理和用户提示
 * 每种错误类型对应不同的处理策略和用户交互方式
 * 
 * @example
 * if (errorDetail.type === ErrorType.NETWORK) {
 *   // 处理网络错误
 * }
 */
export enum ErrorType {
  /**
   * 网络错误
   * 
   * @description
   * 表示由于网络连接问题导致的错误，包括：
   * - 网络断开连接
   * - DNS解析失败  
   * - 网络请求被阻断
   * - 网络不稳定导致的连接中断
   * 
   * @handling_strategy
   * - 提示用户检查网络设置
   * - 提供重试功能
   * - 可以实现自动重连机制
   * 
   * @user_experience
   * - 显示友好的网络错误提示
   * - 提供网络状态检测
   * - 支持离线模式提示
   */
  NETWORK = 'network',

  /**
   * 认证/授权错误
   * 
   * @description  
   * 表示用户身份认证或权限相关的错误，包括：
   * - 未登录或登录过期
   * - 权限不足
   * - Token无效或过期
   * - 账户被禁用或锁定
   * 
   * @handling_strategy
   * - 引导用户重新登录
   * - 清除本地认证信息
   * - 跳转到登录页面
   * - 不建议自动重试
   * 
   * @security_considerations
   * - 敏感操作需要重新验证身份
   * - 避免在日志中记录敏感信息
   * - 实施适当的安全策略
   */
  AUTH = 'auth',

  /**
   * 服务器错误
   * 
   * @description
   * 表示服务器端发生的错误，通常是HTTP 5xx状态码，包括：
   * - 内部服务器错误 (500)
   * - 服务不可用 (503)
   * - 网关错误 (502, 504)
   * - 服务器维护中
   * 
   * @handling_strategy
   * - 提示用户稍后重试
   * - 实现指数退避重试
   * - 记录详细错误日志供排查
   * - 可以降级到缓存数据
   * 
   * @monitoring
   * - 上报到错误监控系统
   * - 触发告警机制
   * - 统计错误频率和影响范围
   */
  SERVER = 'server',

  /**
   * 超时错误
   * 
   * @description
   * 表示请求在规定时间内未能完成，包括：
   * - 连接超时
   * - 读取超时
   * - 写入超时
   * - 自定义业务超时
   * 
   * @handling_strategy
   * - 允许用户重试
   * - 调整超时时间配置
   * - 检查网络连接质量
   * - 优化请求性能
   * 
   * @performance_optimization
   * - 分析超时原因
   * - 优化网络请求
   * - 实现请求分批处理
   */
  TIMEOUT = 'timeout',

  /**
   * 未知错误
   * 
   * @description
   * 表示无法明确分类的错误，作为错误处理的兜底方案，包括：
   * - 意外的JavaScript错误
   * - 第三方库错误
   * - 浏览器兼容性问题
   * - 其他未预期的异常
   * 
   * @handling_strategy
   * - 提供通用的错误提示
   * - 记录详细的错误信息
   * - 提供用户反馈渠道
   * - 实现优雅降级
   * 
   * @debugging
   * - 收集完整的错误堆栈
   * - 记录用户操作路径
   * - 提供错误复现信息
   */
  UNKNOWN = 'unknown'
}

/**
 * 错误详情接口
 * 定义错误处理后返回的标准化错误信息结构
 * 
 * @interface ErrorDetail
 * @description
 * 统一的错误信息格式，包含错误类型、用户友好的消息、技术详情等
 * 便于错误的展示、日志记录和后续处理
 * 
 * @example
 * const errorDetail: ErrorDetail = {
 *   type: ErrorType.NETWORK,
 *   message: '网络连接失败，请检查您的网络设置',
 *   detail: 'TypeError: Failed to fetch',
 *   retry: true
 * };
 */
interface ErrorDetail {
  /**
   * 错误类型
   * 
   * @type {ErrorType}
   * @description
   * 标识错误的分类，用于确定处理策略和用户交互方式
   * 
   * @required 必填字段
   * @usage 用于错误处理逻辑的分支判断
   */
  type: ErrorType;

  /**
   * 用户友好的错误消息
   * 
   * @type {string}
   * @description
   * 面向最终用户的错误提示信息，应该：
   * - 使用通俗易懂的语言
   * - 避免技术术语
   * - 提供解决建议
   * - 保持简洁明了
   * 
   * @required 必填字段
   * @localization 支持多语言本地化
   * 
   * @example
   * "网络连接失败，请检查您的网络设置"
   * "登录已过期，请重新登录"
   */
  message: string;

  /**
   * 技术错误详情 (可选)
   * 
   * @type {string}
   * @optional
   * @description
   * 原始的技术错误信息，主要用于：
   * - 开发调试
   * - 错误日志记录
   * - 技术支持排查
   * - 错误监控上报
   * 
   * @example
   * "TypeError: Failed to fetch"
   * "Request failed with status code 500"
   * 
   * @privacy 注意不要包含敏感信息
   */
  detail?: string;

  /**
   * 错误码 (可选)
   * 
   * @type {string}
   * @optional
   * @description
   * 具体的错误代码，用于精确的错误识别和处理
   * 可以是HTTP状态码、业务错误码或系统错误码
   * 
   * @example
   * "401", "403", "500", "NETWORK_ERROR", "TOKEN_EXPIRED"
   * 
   * @usage
   * - 错误统计和分析
   * - 特定错误的定制处理
   * - 与后端错误码对应
   */
  code?: string;

  /**
   * 是否支持重试 (可选)
   * 
   * @type {boolean}
   * @optional
   * @default true (对大多数错误类型)
   * @description
   * 标识该错误是否适合进行重试操作
   * 用于控制重试按钮的显示和自动重试逻辑
   * 
   * @decision_factors
   * - 网络错误：通常可以重试
   * - 认证错误：通常不应重试
   * - 服务器错误：可以重试，但需要限制次数
   * - 超时错误：可以重试
   * - 客户端错误（4xx）：通常不应重试
   * 
   * @example
   * retry: true  // 显示重试按钮
   * retry: false // 不显示重试按钮
   */
  retry?: boolean;
}

/**
 * 错误处理器类
 * 提供统一的错误处理逻辑，将各种类型的错误转换为标准化的错误详情
 * 
 * @class ErrorHandler
 * @description
 * 错误处理器采用静态方法设计，无需实例化即可使用
 * 负责错误的分类、消息转换和处理策略建议
 * 
 * @example
 * try {
 *   await apiCall();
 * } catch (error) {
 *   const errorDetail = ErrorHandler.handle(error);
 *   showErrorMessage(errorDetail.message);
 *   if (errorDetail.retry) {
 *     showRetryButton();
 *   }
 * }
 * 
 * @design_patterns
 * - 工厂模式：根据错误类型创建相应的错误详情
 * - 策略模式：不同错误类型采用不同的处理策略
 * - 责任链模式：按优先级检查错误类型
 */
export class ErrorHandler {
  /**
   * 错误处理主方法
   * 分析输入的错误对象，返回标准化的错误详情
   * 
   * @static
   * @method handle
   * @param {any} error - 原始错误对象，可以是各种类型的错误
   * @returns {ErrorDetail} 标准化的错误详情对象
   * 
   * @description
   * 该方法按照优先级顺序检查错误类型：
   * 1. 网络错误 - 最常见且用户最容易理解
   * 2. 认证错误 - 需要特殊处理的安全相关错误  
   * 3. 服务器错误 - 后端问题，用户无法直接解决
   * 4. 超时错误 - 可能的网络或性能问题
   * 5. 未知错误 - 兜底处理，确保总有合适的提示
   * 
   * @example
   * // 处理网络请求错误
   * const errorDetail = ErrorHandler.handle(axiosError);
   * 
   * // 处理通用JavaScript错误
   * const errorDetail = ErrorHandler.handle(new Error('Something went wrong'));
   * 
   * @error_detection_logic
   * 错误检测基于多种条件：
   * - 网络状态检查
   * - HTTP状态码分析
   * - 错误消息关键词匹配
   * - 错误代码识别
   * 
   * @extensibility
   * 可以通过修改检查条件来支持新的错误类型或调整检测逻辑
   */
  static handle(error: any): ErrorDetail {
    // ========== 网络错误检测和处理 ==========
    /**
     * 检测网络相关错误
     * 
     * @detection_conditions
     * 1. navigator.onLine === false：浏览器检测到网络断开
     * 2. error.message包含'network'：错误消息中包含网络相关关键词
     * 3. error.message包含'Network'：错误消息中包含网络相关关键词（大写）
     * 
     * @additional_patterns
     * 可以考虑添加的检测模式：
     * - error.code === 'NETWORK_ERROR'
     * - error.message.includes('fetch')
     * - error.name === 'NetworkError'
     */
    if (!navigator.onLine || error.message?.includes('network') || error.message?.includes('Network')) {
      return {
        type: ErrorType.NETWORK,
        message: '网络连接失败，请检查您的网络设置',
        detail: error.message,
        retry: true // 网络错误通常可以重试
      };
    }

    // ========== 认证错误检测和处理 ==========
    /**
     * 检测认证和授权相关错误
     * 
     * @http_status_codes
     * - 401 Unauthorized：未授权，通常表示未登录或token无效
     * - 403 Forbidden：禁止访问，表示权限不足
     * 
     * @handling_strategy
     * - 不建议自动重试，因为重试通常不会解决认证问题
     * - 应该引导用户重新登录或联系管理员
     * - 需要清除本地存储的认证信息
     * 
     * @security_considerations
     * - 避免在错误消息中暴露敏感信息
     * - 记录认证失败事件用于安全审计
     */
    if (error.status === 401 || error.status === 403) {
      return {
        type: ErrorType.AUTH,
        message: '登录已过期，请重新登录',
        detail: error.message,
        code: String(error.status),
        retry: false // 认证错误通常不应该重试
      };
    }

    // ========== 服务器错误检测和处理 ==========
    /**
     * 检测服务器端错误
     * 
     * @http_status_range
     * HTTP 5xx 状态码表示服务器错误：
     * - 500 Internal Server Error：内部服务器错误
     * - 501 Not Implemented：未实现
     * - 502 Bad Gateway：网关错误
     * - 503 Service Unavailable：服务不可用
     * - 504 Gateway Timeout：网关超时
     * 
     * @handling_strategy
     * - 允许重试，但应该有重试限制和延时
     * - 实现指数退避算法避免服务器压力
     * - 提供降级方案或缓存数据
     * 
     * @monitoring_integration
     * - 上报到错误监控系统（如Sentry）
     * - 记录服务器错误的详细信息
     * - 统计错误率和影响范围
     */
    if (error.status >= 500) {
      return {
        type: ErrorType.SERVER,
        message: '服务器暂时不可用，请稍后再试',
        detail: error.message,
        code: String(error.status),
        retry: true // 服务器错误可以重试
      };
    }

    // ========== 超时错误检测和处理 ==========
    /**
     * 检测请求超时错误
     * 
     * @detection_patterns
     * 1. error.message包含'timeout'：通用超时错误
     * 2. error.code === 'ECONNABORTED'：Axios的连接中断错误码
     * 
     * @timeout_types
     * - 连接超时：建立连接耗时过长
     * - 读取超时：服务器响应耗时过长
     * - 写入超时：发送数据耗时过长
     * 
     * @additional_detection
     * 可以考虑添加的检测条件：
     * - error.code === 'ETIMEDOUT'
     * - error.message.includes('Request timeout')
     * - error.name === 'TimeoutError'
     * 
     * @optimization_suggestions
     * - 调整超时时间配置
     * - 优化网络请求大小
     * - 实现请求分片或分批处理
     */
    if (error.message?.includes('timeout') || error.code === 'ECONNABORTED') {
      return {
        type: ErrorType.TIMEOUT,
        message: '请求超时，请检查网络连接',
        detail: error.message,
        retry: true // 超时错误可以重试
      };
    }

    // ========== 未知错误兜底处理 ==========
    /**
     * 处理所有无法明确分类的错误
     * 
     * @fallback_strategy
     * 当错误不符合上述任何类型时，提供通用的错误处理
     * 确保用户总能收到有意义的错误提示
     * 
     * @information_collection
     * - 保留原始错误消息用于调试
     * - 标记为可重试，给用户更多选择
     * - 记录完整的错误信息供后续分析
     * 
     * @improvement_opportunities
     * - 定期分析未知错误的模式
     * - 根据常见的未知错误类型扩展检测逻辑
     * - 收集用户反馈优化错误提示
     * 
     * @debugging_support
     * - 保留原始错误的所有信息
     * - 提供错误上报机制
     * - 支持错误复现和分析
     */
    return {
      type: ErrorType.UNKNOWN,
      message: '发生未知错误，请重试',
      detail: error.message,
      retry: true // 未知错误默认允许重试
    };
  }
}
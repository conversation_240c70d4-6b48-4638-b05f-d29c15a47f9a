<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  value: string
  suit: string // 支持格式如 "SH", "SD", "SS", "SC"
  revealed: boolean
}

const props = withDefaults(defineProps<Props>(), {
  revealed: false,
})

// 背面图
const backBackground = '/assets/card_hidden-a1ff52b5.svg'
const backBackgroundSm = '/assets/card_hidden_sm-e47aadd2.svg'

// 从后端 suit 字符串中提取最后一个字符（H/D/S/C）
const normalizedSuit = computed(() => props.suit.slice(-1).toUpperCase())

// 花色图标映射
const cardSuitIcon = computed(() => {
  const suitMap: Record<string, string> = {
    S: '#icon-Spades',
    H: '#icon-Hearts',
    D: '#icon-Diamonds',
    C: '#icon-Clubs',
  }
  return suitMap[normalizedSuit.value] ?? ''
})

// 花色颜色（红/黑）
const suitColor = computed(() => {
  return normalizedSuit.value === 'S' || normalizedSuit.value === 'C' ? '#000000' : '#d40000'
})

// 正面背景（统一白色）
const suitBackground = computed(() => '#ffffff')
</script>

<template>
  <div class="card-container" data-type="card">
    <div class="card-inner" :class="{ revealing: revealed }" :style="{ transform: revealed ? 'rotateY(180deg)' : 'rotateY(0deg)' }">
      <!-- 背面 -->
      <div class="card card-front" :style="{
        '--background': `url(${backBackground})`,
        '--background-sm': `url(${backBackgroundSm})`
      }"></div>

      <!-- 正面 -->
      <div class="card card-back" :data-role="'card-layout'" :data-value="value" :data-suit="suit" :style="{
        color: suitColor,
        background: suitBackground
      }">
        <span class="card-value">{{ value }}</span>
        <svg class="card-suit-icon">
          <use :href="cardSuitIcon"></use>
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.card-container {
  perspective: 1000px;
  width: 2.625em;
  height: 3.375em;
  max-width: 2.625em;
  max-height: 3.375em;
  position: relative;
}

.card-inner {
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  position: relative;
}
.card-inner.revealing {
  animation: flip 0.6s forwards;
}

.card {
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: cover;
  border-radius: 0.225rem;
  display: flex;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  user-select: none;
  pointer-events: none;
  filter: drop-shadow(-1px 0px 4px rgba(0, 0, 0, 0.39));
}

.card-front {
  background-image: var(--background);
  background-size: cover;
}

.card-back {
  transform: rotateY(180deg);
  background: #fff;
  color: #000;
}

.card-value {
  position: absolute;
  top: -0.09375em;
  left: 0.25em;
  font-size: 1.5em;
  font-family: 'Roboto Condensed', sans-serif;
  font-weight: 500;
  letter-spacing: -0.1em;
  line-height: 1.3;
}

.card-suit-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.375em;
  height: 2.375em;
}
/* ✅ 4K 显示器（≥2560px） */
@media (min-width: 2560px) {
  .card-container {
    width: 4.2em;
    height: 5.4em;
  }

  .card-value {
    font-size: 2.5em;
    height: auto;
    width: auto;
  }

  .card-suit-icon {
    width: 3.5em;
    height: 3.5em;
    transform: translate(-50%, -50%);
  }
}

/* ✅ 2K 显示器（1440px ～ 2559px） */
@media (min-width: 1440px) and (max-width: 2559px) {
  .card-container {
    width: 3.2em;
    height: 4.125em;
  }

  .card-value {
    font-size: 1.25em;
  }

  .card-suit-icon {
    width: 1.25em;
    height: 1.25em;
    transform: translate(-50%, 0%);
  }
}

/* ✅ 1080p 常规显示器（1024px ～ 1439px） */
@media (min-width: 1024px) and (max-width: 1439px) {
  .card-container {
    width: 2.625em;
    height: 3.375em;
  }

  .card-value {
    font-size: 1.5em;
  }

  .card-suit-icon {
    width: 1.475em;
    height: 1.475em;
    transform: translate(-50%, -5%);
  }
}

/* ✅ 平板设备（768px ～ 1023px） */
@media (min-width: 768px) and (max-width: 1023px) {
  .card-container {
    width: 2.2em;
    height: 2.875em;
  }

  .card-value {
    font-size: 1.2em;
  }

  .card-suit-icon {
    width: 1.375em;
    height: 1.375em;
    transform: translate(-50%, -20%);
  }
}

/* ✅ 小屏手机（≤768px） */
@media (max-width: 768px) {
  .card-container {
    width: 2.1875em;
    height: 2.875em;
  }

  .card-value {
    font-size: 1.078125em;
    height: 1.3125em;
    width: 1.375em;
  }

  .card-suit-icon {
    width: 1.375em;
    height: 1.375em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -20%);
  }
}

/* ✅ 超小屏手机（≤480px） */
@media (max-width: 480px) {
  .card-container {
    width: 1.75em;
    height: 2.3em;
  }

  .card-value {
    font-size: 0.95em;
  }

  .card-suit-icon {
    width: 0.95em;
    height: 0.95em;
    transform: translate(-50%, -15%);
  }
}


@keyframes flip {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(180deg);
  }
}
</style>
import { defineStore } from 'pinia'
import type {
  IssueOpeningViewObject,
  IssueStopingViewObject,
  IssueFinishedViewObject,
  LotterySettlementViewObject
} from '@/scripts/signalr/hubs/default-hub'
import type { GamePlayType } from '@/types/game'

type DrawRecord = {
  issueNumber: string
  drawNumber: string[]
}

export const useGameStore = defineStore('game', {
  state: () => ({
    // 当前游戏阶段
    phase: 'idle' as 'idle' | 'opening' | 'stoping' | 'finished' | 'settled',

    // 开奖结果（如 ['SD', '5']）
    result: [] as string[],

    // 倒计时毫秒值（由 OpeningAsync 提供）
    countdown: 0,

    // 当前期号
    issueNumber: '',

    // 结算信息
    settlement: null as LotterySettlementViewObject | null,

    // 控制动画结束后再翻牌
    revealed: false,

    // 历史记录
    recentDraws: [] as { issueNumber: string; drawNumber: string[] }[],

    // 游戏玩法数据
    gamePlayTypes: [] as GamePlayType[],
  }),

  getters: {
    /**
     * 获取倒计时秒数（四舍五入）
     */
    countdownSeconds: (state) => Math.max(0, Math.floor(state.countdown)),
    latestDraw: (state) => state.recentDraws[0] || null,

    /**
     * 获取数字类型的游戏玩法（2-10, A, J, Q, K）
     * lotteryGameType = 0
     */
    numberGameTypes: (state) => state.gamePlayTypes.filter(game => game.lotteryGameType === 0),

    /**
     * 获取黑桃类型的游戏玩法
     * lotteryGameType = 8
     */
    spadesGameTypes: (state) => state.gamePlayTypes.filter(game => game.lotteryGameType === 8),

    /**
     * 获取其他花色类型的游戏玩法（红桃、梅花、方块）
     * lotteryGameType = 9 且是花色
     */
    suitGameTypes: (state) => state.gamePlayTypes.filter(game =>
      game.lotteryGameType === 9 &&
      ['PokerHearts', 'PokerClubs', 'PokerDiamonds'].includes(game.playingTypeId)
    ),

    /**
     * 获取大小类型的游戏玩法
     * lotteryGameType = 9 且是大小
     */
    sizeGameTypes: (state) => state.gamePlayTypes.filter(game =>
      game.lotteryGameType === 9 &&
      ['Large', 'Small'].includes(game.playingTypeId)
    ),

    /**
     * 获取所有花色类型（包括黑桃）
     */
    allSuitGameTypes: (state) => state.gamePlayTypes.filter(game =>
      ['PokerSpades', 'PokerHearts', 'PokerClubs', 'PokerDiamonds'].includes(game.playingTypeId)
    )
  },

  actions: {
    /**
     * 开盘阶段：收到 OpeningAsync 消息
     */
    handleIssueOpening(event: IssueOpeningViewObject) {
      this.phase = 'opening'
      this.issueNumber = event.issueNumber
      this.countdown = event.countdown // 以毫秒为单位
      this.revealed = true // 开盘时允许投注，隐藏蒙版
    },

    /**
     * 停止下注阶段：收到 StopingAsync 消息
     * 封盘状态，显示蒙版阻止投注
     */
    handleIssueStoping(event: IssueStopingViewObject) {
      this.phase = 'stoping'
      this.issueNumber = event.issueNumber
      this.countdown = event.countdown // 保持倒计时，用于显示"封"字期间的倒计时
      // 封盘时不设置 revealed，让 TabBar 显示蒙版
    },

    /**
     * 开奖阶段：收到 FinishedAsync 消息
     */
    handleIssueFinished(event: IssueFinishedViewObject) {
      this.phase = 'finished'
      this.issueNumber = event.issueNumber
      this.result = event.drawNumber

      // 添加到历史记录头部，去重并保留最多10条
      const exists = this.recentDraws.find(d => d.issueNumber === event.issueNumber)
      if (!exists) {
        this.recentDraws.unshift({
          issueNumber: event.issueNumber,
          drawNumber: event.drawNumber
        })
        this.recentDraws = this.recentDraws.slice(0, 10)
      }

      // 🔥 开奖结束，触发清空投注金额事件
      console.log('🎯 [GameStore] 开奖结束，触发清空投注金额事件')
      window.dispatchEvent(new CustomEvent('clearBetAmounts', {
        detail: {
          issueNumber: event.issueNumber,
          drawNumber: event.drawNumber
        }
      }))
    },
    

    /**
     * 结算阶段：收到 LotterySettlement 消息
     */
    handleLotterySettlement(event: LotterySettlementViewObject) {
      this.phase = 'settled'
      this.settlement = event
    },


    handleIssueOptions(event: any) {
     
    },

    setRecentDraws(draws: DrawRecord[]) {
      this.recentDraws = [...draws].sort((a, b) =>
        a.issueNumber > b.issueNumber ? -1 : 1
      )
    },

    /**
     * 设置游戏玩法数据
     */
    setGamePlayTypes(gamePlayTypes: GamePlayType[]) {
      this.gamePlayTypes = gamePlayTypes
    },

    /**
     * 重置所有状态
     */
    reset() {
      this.phase = 'idle'
      this.countdown = 0
      this.result = []
      this.issueNumber = ''
      this.settlement = null
      this.revealed = false
    }
  }
})

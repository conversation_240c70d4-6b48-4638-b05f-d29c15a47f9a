import { defineStore } from 'pinia'

export interface NotificationState {
  /** 当前显示的消息 */
  message: string
  /** 消息类型 */
  type: 'info' | 'warning' | 'error' | 'success'
  /** 是否显示 */
  show: boolean
  /** 自动隐藏时间（毫秒），0表示不自动隐藏 */
  autoHide: number
}

export const useNotificationStore = defineStore('notification', {
  state: (): NotificationState => ({
    message: '',
    type: 'info',
    show: false,
    autoHide: 0
  }),

  actions: {
    /**
     * 显示通知消息
     * @param message 消息内容
     * @param type 消息类型
     * @param autoHide 自动隐藏时间（毫秒），默认0不自动隐藏
     */
    showNotification(message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info', autoHide: number = 0) {
      this.message = message
      this.type = type
      this.show = true
      this.autoHide = autoHide

      // 如果设置了自动隐藏时间，则自动隐藏
      if (autoHide > 0) {
        setTimeout(() => {
          this.hideNotification()
        }, autoHide)
      }
    },

    /**
     * 显示游戏状态信息（不自动隐藏）
     * @param message 状态消息
     */
    showGameStatus(message: string) {
      this.showNotification(message, 'warning', 0)
    },

    /**
     * 显示成功消息
     * @param message 消息内容
     * @param autoHide 自动隐藏时间，默认3秒
     */
    showSuccess(message: string, autoHide: number = 3000) {
      this.showNotification(message, 'success', autoHide)
    },

    /**
     * 显示错误消息
     * @param message 消息内容
     * @param autoHide 自动隐藏时间，默认5秒
     */
    showError(message: string, autoHide: number = 5000) {
      this.showNotification(message, 'error', autoHide)
    },

    /**
     * 显示信息消息
     * @param message 消息内容
     * @param autoHide 自动隐藏时间，默认3秒
     */
    showInfo(message: string, autoHide: number = 3000) {
      this.showNotification(message, 'info', autoHide)
    },

    /**
     * 隐藏通知
     */
    hideNotification() {
      this.show = false
      this.message = ''
    },

    /**
     * 清空通知
     */
    clearNotification() {
      this.message = ''
      this.type = 'info'
      this.show = false
      this.autoHide = 0
    }
  }
})

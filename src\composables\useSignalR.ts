import { useGameStore } from '@/stores/game'
import { useAuthStore } from '@/stores/auth'
import { defaultHub } from '@/scripts/signalr'
import socketClient from '@/scripts/utils/socket-client'
import { request } from '@/scripts/request'


export function useSignalR(showNotification: (msg: string) => void, done: () => void,authorizeCode: string ) {
  const gameStore = useGameStore()
  const authStore = useAuthStore()

  const init = async () => {
    try {
      // 设置 SignalR 的错误处理回调
      socketClient.setErrorHandler((errorMsg) => {
        console.log('SignalR 错误:', errorMsg) // 添加日志便于调试
        showNotification(errorMsg)
      })
      
      // 设置重连回调
      socketClient.setReconnectingHandler((msg) => {
        showNotification(msg)
      })
      
      // 设置重连成功回调
      socketClient.setReconnectedHandler((msg) => {
        showNotification(msg)
      })

      // const authorizeCode = getAuthorizeCodeFromUrl()
      // const authorizeCode= 'user=%7B%22id%22%3A922856741%2C%22first_name%22%3A%22%E5%A4%A7%E5%88%80%E9%98%9F%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22cc5999%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FLGYfIoNK8UTm_NNklP8c3OEB7KmEDVbaZ9tuZQhQX_w.svg%22%7D&chat_instance=6029751626564489396&chat_type=sender&auth_date=1750573376&signature=TEZqMDDWSNU7M14JqVc7jE-F2X4h93wsysHSTYB9LK2Wf0rlNA1OwucGTTZGEUa6fTmjQ-yo9ob8cmJ__PyRBQ&hash=57de87c6f03913ecadcbec3fb8879637fe9ad34879724b9c385456d3eba10285'
      const botId = '7637401882'
      if (!authorizeCode) {
        showNotification('❗ 缺少 authorizeCode 参数')
        return
      }

      showNotification('正在登录...')
      
      try {
        const token = await authStore.signInAsync(authorizeCode, botId)
        const auth = authStore.signInSuccess(token)
        if (!auth) {
          showNotification('❗ 登录失败，请检查授权码')
          return
        }

        // 认证成功后，获取游戏玩法数据
        try {
          showNotification('正在获取游戏数据...')
          const gamesResponse = await request({
            url: '/lottery/games',
            method: 'GET'
          })

          console.log('🎮 游戏玩法数据:', gamesResponse.data)
          console.log('🎮 游戏玩法详情:', JSON.stringify(gamesResponse.data, null, 2))

          // 将游戏数据存储到store中
          gameStore.setGamePlayTypes(gamesResponse.data)
        } catch (gameError) {
          console.error('获取游戏数据失败:', gameError)
          showNotification('⚠️ 获取游戏数据失败，但不影响游戏进行')
        }

        // 认证成功后，建立SignalR连接
        showNotification('正在连接服务器...')
        defaultHub.hub.connect()
      } catch (authError) {
        console.error('认证失败:', authError)
        showNotification('❗ 认证失败，请检查网络连接或授权码')
        return
      }

      // 注册事件监听
      defaultHub.hub.on(defaultHub.EventNames.onWelcome, async (event: string) => {
        showNotification(`🎉 欢迎：${event}`)
        try {
          defaultHub.hub.send("Enter", "Tbhp")

          const draws = await defaultHub.hub.invoke("RecentlyDrawed", 10)
          if (Array.isArray(draws)) {
            gameStore.setRecentDraws(draws)
          }
          
          done()
        } catch (sendError) {
          console.error('发送消息失败:', sendError)
          showNotification('❌ 发送消息失败，请重试')
        }
      })

      defaultHub.hub.on(defaultHub.EventNames.onIssueOpening, gameStore.handleIssueOpening)
      defaultHub.hub.on(defaultHub.EventNames.onIssueStoping, gameStore.handleIssueStoping)
      defaultHub.hub.on(defaultHub.EventNames.onIssueFinished, gameStore.handleIssueFinished)
      defaultHub.hub.on(defaultHub.EventNames.onLotterySettled, gameStore.handleLotterySettlement)
      defaultHub.hub.on(defaultHub.EventNames.onIssueOptions, gameStore.handleIssueOptions)

      defaultHub.hub.on(defaultHub.EventNames.reconnecting, () => showNotification('🔌 正在重新连接...'))
      defaultHub.hub.on(defaultHub.EventNames.closed, () => showNotification('❌ 服务器连接已关闭'))
    } catch (error) {
      console.error('SignalR 初始化失败:', error)
      showNotification('🚫 初始化失败，请稍后重试')
    }
  }

  return { init }
}
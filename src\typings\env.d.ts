// env.d.ts
/// <reference types="vite/client" />

declare namespace NodeJS {
  interface ImportMetaEnv {
    /** The base url of the application */
    readonly VITE_BASE_URL: string;
    /** The title of the application */
    readonly VITE_APP_TITLE: string;
    /** The description of the application */
    readonly VITE_APP_DESC: string;
    /** Whether the source map is enable */
    readonly VITE_SOURCE_MAP: boolean;
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}

// 👇 在这里扩展 window.Telegram 类型
import type { Telegram } from '@twa-dev/types'

declare global {
  interface Window {
    Telegram: Telegram
  }
}

export {}
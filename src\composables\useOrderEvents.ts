import { onMounted, onUnmounted } from 'vue'
import { useNotificationStore } from '@/stores/notification'
import { useAuthStore } from '@/stores/auth'
import type {
  LotteryOrderSuccessEto,
  LotteryOrderFailureEto,
  LotteryOrderCancelledEto,
  LotterySettlementViewObject
} from '@/scripts/signalr/hubs/default-hub'

/**
 * 监听投注订单相关的 SignalR 事件
 */
export function useOrderEvents() {
  const notificationStore = useNotificationStore()
  const authStore = useAuthStore()

  /**
   * 处理投注成功事件
   */
  const handleOrderSuccess = (event: CustomEvent<LotteryOrderSuccessEto>) => {
    const data = event.detail
    console.log('🎉 [Socket] 收到投注成功事件:', data)

    // 更新余额
    authStore.updateBalance(data.balance)

    // 显示成功通知到 ChatBar
    notificationStore.showSuccess(
      `投注成功！玩法：${data.playingTypeName}，金额：${data.amount}，余额：${data.balance}`,
      0  // 不自动隐藏，让用户手动查看
    )

    // 这里可以添加其他业务逻辑
    // 例如：刷新投注记录等
  }

  /**
   * 处理投注失败事件
   */
  const handleOrderFailure = (event: CustomEvent<LotteryOrderFailureEto>) => {
    const data = event.detail
    console.log('❌ [Socket] 收到投注失败事件:', data)
    console.log('❌ [Socket] 失败消息:', data.message)

    // 显示失败通知到 HeaderBar
    notificationStore.showError(
      `投注失败：${data.message}`,
      0  // 不自动隐藏，让用户手动查看
    )
  }

  /**
   * 处理订单取消事件
   */
  const handleOrderCancelled = (event: CustomEvent<LotteryOrderCancelledEto>) => {
    const data = event.detail
    console.log('🚫 收到订单取消事件:', data)

    // 显示取消通知
    notificationStore.showInfo(
      `订单已取消，期号：${data.issueNumber}`,
      3000
    )

    // 这里可以添加其他业务逻辑
    // 例如：清空投注记录、刷新界面等
  }

  /**
   * 处理结算事件
   */
  const handleSettlement = (event: CustomEvent<LotterySettlementViewObject>) => {
    const data = event.detail
    console.log('💰 [Socket] 收到结算事件:', data)

    // 如果有返奖金额，更新余额并显示返奖提示
    if (data.amountAfterTax && data.amountAfterTax > 0) {
      // 更新余额
      const newBalance = authStore.balance + data.amountAfterTax
      authStore.updateBalance(newBalance)

      // 触发返奖提示事件
      window.dispatchEvent(new CustomEvent('showWinningAmount', {
        detail: {
          amount: data.amountAfterTax,
          orderId: data.orderId,
          playingTypeId: data.playingTypeId
        }
      }))

      console.log(`💰 [Socket] 返奖 ${data.amountAfterTax}，新余额: ${newBalance}`)

      // 显示成功通知
      notificationStore.showSuccess(
        `恭喜中奖！返奖金额：${data.amountAfterTax}`,
        0
      )
    }
  }

  /**
   * 注册事件监听器
   */
  const registerEventListeners = () => {
    console.log('📝 [useOrderEvents] 注册事件监听器')
    window.addEventListener('lotteryOrderSuccess', handleOrderSuccess as EventListener)
    window.addEventListener('lotteryOrderFailure', handleOrderFailure as EventListener)
    window.addEventListener('lotteryOrderCancelled', handleOrderCancelled as EventListener)
    window.addEventListener('lotterySettlement', handleSettlement as EventListener)
    console.log('✅ [useOrderEvents] 事件监听器注册完成')
  }

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    window.removeEventListener('lotteryOrderSuccess', handleOrderSuccess as EventListener)
    window.removeEventListener('lotteryOrderFailure', handleOrderFailure as EventListener)
    window.removeEventListener('lotteryOrderCancelled', handleOrderCancelled as EventListener)
    window.removeEventListener('lotterySettlement', handleSettlement as EventListener)
  }

  // 在组件挂载时注册事件监听器
  onMounted(() => {
    registerEventListeners()
  })

  // 在组件卸载时移除事件监听器
  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    handleOrderSuccess,
    handleOrderFailure,
    handleOrderCancelled,
    handleSettlement,
    registerEventListeners,
    removeEventListeners
  }
}

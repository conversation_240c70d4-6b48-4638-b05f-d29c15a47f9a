import { onMounted, onUnmounted } from 'vue'
import { useNotificationStore } from '@/stores/notification'
import type { 
  LotteryOrderSuccessEto, 
  LotteryOrderFailureEto, 
  LotteryOrderCancelledEto 
} from '@/scripts/signalr/hubs/default-hub'

/**
 * 监听投注订单相关的 SignalR 事件
 */
export function useOrderEvents() {
  const notificationStore = useNotificationStore()

  /**
   * 处理投注成功事件
   */
  const handleOrderSuccess = (event: CustomEvent<LotteryOrderSuccessEto>) => {
    const data = event.detail
    console.log('🎉 收到投注成功事件:', data)
    
    // 显示成功通知
    notificationStore.showSuccess(
      `投注成功！玩法：${data.playingTypeName}，金额：${data.amount}，余额：${data.balance}`,
      5000
    )
    
    // 这里可以添加其他业务逻辑
    // 例如：更新余额显示、刷新投注记录等
  }

  /**
   * 处理投注失败事件
   */
  const handleOrderFailure = (event: CustomEvent<LotteryOrderFailureEto>) => {
    const data = event.detail
    console.log('❌ 收到投注失败事件:', data)
    
    // 显示失败通知
    notificationStore.showError(
      `投注失败：${data.message}`,
      5000
    )
  }

  /**
   * 处理订单取消事件
   */
  const handleOrderCancelled = (event: CustomEvent<LotteryOrderCancelledEto>) => {
    const data = event.detail
    console.log('🚫 收到订单取消事件:', data)
    
    // 显示取消通知
    notificationStore.showInfo(
      `订单已取消，期号：${data.issueNumber}`,
      3000
    )
    
    // 这里可以添加其他业务逻辑
    // 例如：清空投注记录、刷新界面等
  }

  /**
   * 注册事件监听器
   */
  const registerEventListeners = () => {
    window.addEventListener('lotteryOrderSuccess', handleOrderSuccess as EventListener)
    window.addEventListener('lotteryOrderFailure', handleOrderFailure as EventListener)
    window.addEventListener('lotteryOrderCancelled', handleOrderCancelled as EventListener)
  }

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    window.removeEventListener('lotteryOrderSuccess', handleOrderSuccess as EventListener)
    window.removeEventListener('lotteryOrderFailure', handleOrderFailure as EventListener)
    window.removeEventListener('lotteryOrderCancelled', handleOrderCancelled as EventListener)
  }

  // 在组件挂载时注册事件监听器
  onMounted(() => {
    registerEventListeners()
  })

  // 在组件卸载时移除事件监听器
  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    handleOrderSuccess,
    handleOrderFailure,
    handleOrderCancelled,
    registerEventListeners,
    removeEventListeners
  }
}

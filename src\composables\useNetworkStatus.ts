// composables/useNetworkStatus.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const networkType = ref<string | null>(null)
  
  // 如果浏览器支持Connection API
  if ('connection' in navigator) {
    // @ts-ignore - Connection API不在所有TS定义中
    networkType.value = (navigator.connection || {}).effectiveType
  }
  
  const updateOnlineStatus = () => {
    isOnline.value = navigator.onLine
  }
  
  const handleConnectionChange = () => {
    if ('connection' in navigator) {
      // @ts-ignore
      networkType.value = (navigator.connection || {}).effectiveType
    }
  }
  
  onMounted(() => {
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    if ('connection' in navigator) {
      // @ts-ignore
      navigator.connection?.addEventListener('change', handleConnectionChange)
    }
  })
  
  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
    
    if ('connection' in navigator) {
      // @ts-ignore
      navigator.connection?.removeEventListener('change', handleConnectionChange)
    }
  })
  
  return { isOnline, networkType }
}
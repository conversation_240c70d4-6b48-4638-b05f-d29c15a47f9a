@font-face {
  font-family: 'MiSans';
  src: url('/assets/fonts/MiSans-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Normal 和 Regular 同一个weight（400），可以只用一个 */

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Demibold 大部分是 600，等同于 Semibold，可以不用重复 */

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MiSans';
  src: url('./assets/fonts/MiSans-Heavy.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* 如果有 Black（900字重），目前没有，就不用了 */


:root {
  font-family: 'MiSans', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow: hidden;
  height: 100%;
  touch-action: pan-x pan-y;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  scrollbar-width: none;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
} */
button:focus,
button:focus-visible {
  outline: 0px auto -webkit-focus-ring-color;
}

/* .card {
  padding: 2em;
} */

#app {
  min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    background: url(/assets/bg.jpg) #010004;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */

import type { WithIndex } from '@/scripts/signalr/with-index';
import { type Hand<PERSON>, HubContext, type IHubContext } from '@/scripts/signalr/hub-context';

export enum DefaultHubEventNames {
  reconnected = 'reconnected',
  reconnecting = 'reconnecting',
  closed = 'closed',
  onMessage = 'OnMessage',
  onWelcome = 'WelcomeAsync',
  onIssueOpening = 'OpeningAsync',
  onIssueStoping = 'StopingAsync',
  onIssueFinished = 'FinishedAsync',
  onLotterySettled = 'SettlementAsync',
  onIssueOptions = 'IssueOptionsAsync',
  onLotteryOrderSuccess = 'LotteryOrderSuccessAsync',
  onLotteryOrderFailure = 'LotteryOrderFailureAsync',
  onLotteryOrderCancelled = 'LotteryOrderCancelledAsync'
}
export interface MessageEto {
  source: string;
  message: string;
}



export interface IssueOpeningViewObject {
  issueNumber: string
  openTime: string // ISO 格式日期字符串，如 "2025-05-05T08:00:00Z"
  stopTime: string
  countdown: number
}
export interface IssueStopingViewObject {
  issueNumber: string
  countdown: number
}
export interface IssueFinishedViewObject {
  issueNumber: string
  drawNumber: string[]
}
export interface LotterySettlementViewObject {
  orderId: string
  userId: string
  playingTypeId: string
  currencyId: string
  winingState: WinningStatus
  winningNumbers?: string[]
  drawingNumbers: string[]
  amount: number
  amountAfterTax: number
  settlementTime: string // ISO 日期字符串
}

/**
 * 投注订单成功事件对象
 */
export interface LotteryOrderSuccessEto {
  /** 消息Id */
  messageId?: string
  /** 订单Id */
  orderId: string
  /** 租户Id */
  tenantId: string
  /** 玩家Id */
  playerId: string
  /** Telegram用户Id */
  tgUserId: string
  /** 商户Id */
  merchantId: string
  /** 用户Id */
  merchantUserId: string
  /** 玩法名称 */
  playingTypeName: string
  /** 投注号码 */
  bettingNumber: string[]
  /** 订单金额 */
  amount: number
  /** 余额 */
  balance: number
  /** 订单类型 */
  orderType: number
}

/**
 * 投注订单失败事件对象
 */
export interface LotteryOrderFailureEto {
  /** 消息Id */
  messageId?: string
  /** 玩家Id */
  playerId: string
  /** 租户Id */
  tenantId: string
  /** Telegram用户Id */
  tgUserId: string
  /** 订单类型 */
  orderType: number
  /** 失败消息 */
  message: string
}

/**
 * 投注订单取消事件对象
 */
export interface LotteryOrderCancelledEto {
  /** 消息Id */
  messageId?: string
  /** 玩家Id */
  playerId: string
  /** 租户Id */
  tenantId: string
  /** Telegram用户Id */
  tgUserId: string
  /** 期号 */
  issueNumber: string
  /** 是否私有 */
  isPrivate: boolean
}

export interface IssueOptionsViewObject {
  tgGroupId: string
  autoStopIdleCount: number
  tgGroupRtpRate: number
  merchantId: string
  merchantNotifyAddress: string
  lotteryTypeId: string
  issueGameOptions: Record<string, IssueGameOptions>
}

export interface IssueGameOptions {
  lotteryGameId: string
  lotteryTypeId: string
  lotteryTypeName: string
  playingTypeId: string
  playingTypeName: string
  playingTypeValue: string[]
  lotteryGameType: LotteryGameType
  rebateType: RebateType
  rebateRate: number
  winningRate: number
  bettingOdds: number
  gameLimitOptions: GameLimitOptionsDto[]
}

export type LotteryGameType =
  | 'Precision'           // 直选
  | 'Consecutive'         // 顺子
  | 'Triples'             // 豹子
  | 'Pairs'               // 对子
  | 'Sum'                 // 和值
  | 'SumDigitAttr'        // 和值数字属性（大、小、单、双...）
  | 'SumTail'             // 和尾
  | 'SumTailDigitAttr'    // 和尾数字属性
  | 'PokerSuit'           // 扑克花色
  | 'PockerRankSize'      // 扑克牌面大小

export type RebateType =
  | 'None'              // 不反水
  | 'TotalBetting'      // 总投注
  | 'TotalTurnover'     // 总流水
  | 'NegativeProfit'    // 负盈利

export interface GameLimitOptionsDto {
  currencyId: string
  unitPriceAmount: number
  perWagerMaxLimit: number
  perIssueMaxLimit: number
}




export type WinningStatus = 'NotWinning' | 'Winning' | 'Refunded'


interface DefaultHubEvents {
  [DefaultHubEventNames.reconnecting]: () => void;
  [DefaultHubEventNames.reconnected]: () => void;
  [DefaultHubEventNames.closed]: () => void;
  [DefaultHubEventNames.onWelcome]: (event: string) => void;
  [DefaultHubEventNames.onMessage]: (event: MessageEto) => void;


  [DefaultHubEventNames.onIssueOpening]: (event: IssueOpeningViewObject) => void
  [DefaultHubEventNames.onIssueStoping]: (event: IssueStopingViewObject) => void
  [DefaultHubEventNames.onIssueFinished]: (event: IssueFinishedViewObject) => void
  [DefaultHubEventNames.onLotterySettled]: (event: LotterySettlementViewObject) => void
  [DefaultHubEventNames.onIssueOptions]: (event: IssueOptionsViewObject) => void
  [DefaultHubEventNames.onLotteryOrderSuccess]: (event: LotteryOrderSuccessEto) => void
  [DefaultHubEventNames.onLotteryOrderFailure]: (event: LotteryOrderFailureEto) => void
  [DefaultHubEventNames.onLotteryOrderCancelled]: (event: LotteryOrderCancelledEto) => void

}

class DefaultHub {
  private hub = 'gaming';
  private hubcontext: IHubContext<WithIndex<DefaultHubEvents>>;

  constructor() {
    this.hubcontext = new HubContext<WithIndex<DefaultHubEvents>>(this.hub);
    this.init();
  }

  init() {
    this.on(DefaultHubEventNames.reconnecting, this.onReconnecting);
    this.on(DefaultHubEventNames.reconnected, this.onReconnected);
    this.on(DefaultHubEventNames.closed, this.onClosed);

    // 添加新的投注订单事件监听器
    this.on(DefaultHubEventNames.onLotteryOrderSuccess, this.onLotteryOrderSuccess);
    this.on(DefaultHubEventNames.onLotteryOrderFailure, this.onLotteryOrderFailure);
    this.on(DefaultHubEventNames.onLotteryOrderCancelled, this.onLotteryOrderCancelled);

    console.log('hub init', this.hubcontext.all);
  }

  /**
   * 建立SignalR连接
   * 应该在用户认证完成后调用
   */
  connect() {
    this.hubcontext.connect();
  }

  onReconnecting() {
    console.log(this.hub, 'reconnecting');
  }

  onReconnected() {
    console.log(this.hub, 'reconnected');
  }

  onClosed() {
    console.log(this.hub, 'closed');
  }

  /**
   * 投注订单成功事件处理
   */
  onLotteryOrderSuccess(event: LotteryOrderSuccessEto) {
    console.log('🎉 投注成功:', event);

    // 这里可以触发自定义事件，让其他组件监听
    // 或者直接在这里处理业务逻辑
    window.dispatchEvent(new CustomEvent('lotteryOrderSuccess', { detail: event }));
  }

  /**
   * 投注订单失败事件处理
   */
  onLotteryOrderFailure(event: LotteryOrderFailureEto) {
    console.log('❌ [SignalR] 收到投注失败事件:', event);
    console.log('❌ [SignalR] 失败消息:', event.message);

    // 触发自定义事件
    const customEvent = new CustomEvent('lotteryOrderFailure', { detail: event });
    console.log('❌ [SignalR] 触发自定义事件:', customEvent);
    window.dispatchEvent(customEvent);
  }

  /**
   * 投注订单取消事件处理
   */
  onLotteryOrderCancelled(event: LotteryOrderCancelledEto) {
    console.log('🚫 订单取消:', event);

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('lotteryOrderCancelled', { detail: event }));
  }

  on<Key extends keyof DefaultHubEvents>(type: Key, handler: DefaultHubEvents[Key]): void {
    this.hubcontext.on(type, handler as Handler<DefaultHubEvents[Key]>);
  }

  send(type: string, ...args: any[]): void {
    this.hubcontext.send(type, ...args);
  }

  invoke(type: string, ...args: any[]): any {
    return this.hubcontext.invoke(type, ...args) ;
  }

}

const hub = new DefaultHub();
const EventNames = {
  ...DefaultHubEventNames
} as const;

export default { hub, EventNames };

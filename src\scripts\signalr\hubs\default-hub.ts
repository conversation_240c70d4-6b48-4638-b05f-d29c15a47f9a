import type { WithIndex } from '@/scripts/signalr/with-index';
import { type Hand<PERSON>, HubContext, type IHubContext } from '@/scripts/signalr/hub-context';

export enum DefaultHubEventNames {
  reconnected = 'reconnected',
  reconnecting = 'reconnecting',
  closed = 'closed',
  onMessage = 'OnMessage',
  onWelcome = 'WelcomeAsync',
  onIssueOpening = 'OpeningAsync',
  onIssueStoping = 'StopingAsync',
  onIssueFinished = 'FinishedAsync',
  onLotterySettled = 'SettlementAsync',
  onIssueOptions = 'IssueOptionsAsync'
}
export interface MessageEto {
  source: string;
  message: string;
}



export interface IssueOpeningViewObject {
  issueNumber: string
  openTime: string // ISO 格式日期字符串，如 "2025-05-05T08:00:00Z"
  stopTime: string
  countdown: number
}
export interface IssueStopingViewObject {
  issueNumber: string
  countdown: number
}
export interface IssueFinishedViewObject {
  issueNumber: string
  drawNumber: string[]
}
export interface LotterySettlementViewObject {
  orderId: string
  userId: string
  playingTypeId: string
  currencyId: string
  winingState: WinningStatus
  winningNumbers?: string[]
  drawingNumbers: string[]
  amount: number
  amountAfterTax: number
  settlementTime: string // ISO 日期字符串
}

export interface IssueOptionsViewObject {
  tgGroupId: string
  autoStopIdleCount: number
  tgGroupRtpRate: number
  merchantId: string
  merchantNotifyAddress: string
  lotteryTypeId: string
  issueGameOptions: Record<string, IssueGameOptions>
}

export interface IssueGameOptions {
  lotteryGameId: string
  lotteryTypeId: string
  lotteryTypeName: string
  playingTypeId: string
  playingTypeName: string
  playingTypeValue: string[]
  lotteryGameType: LotteryGameType
  rebateType: RebateType
  rebateRate: number
  winningRate: number
  bettingOdds: number
  gameLimitOptions: GameLimitOptionsDto[]
}

export type LotteryGameType =
  | 'Precision'           // 直选
  | 'Consecutive'         // 顺子
  | 'Triples'             // 豹子
  | 'Pairs'               // 对子
  | 'Sum'                 // 和值
  | 'SumDigitAttr'        // 和值数字属性（大、小、单、双...）
  | 'SumTail'             // 和尾
  | 'SumTailDigitAttr'    // 和尾数字属性
  | 'PokerSuit'           // 扑克花色
  | 'PockerRankSize'      // 扑克牌面大小

export type RebateType =
  | 'None'              // 不反水
  | 'TotalBetting'      // 总投注
  | 'TotalTurnover'     // 总流水
  | 'NegativeProfit'    // 负盈利

export interface GameLimitOptionsDto {
  currencyId: string
  unitPriceAmount: number
  perWagerMaxLimit: number
  perIssueMaxLimit: number
}




export type WinningStatus = 'NotWinning' | 'Winning' | 'Refunded'


interface DefaultHubEvents {
  [DefaultHubEventNames.reconnecting]: () => void;
  [DefaultHubEventNames.reconnected]: () => void;
  [DefaultHubEventNames.closed]: () => void;
  [DefaultHubEventNames.onWelcome]: (event: string) => void;
  [DefaultHubEventNames.onMessage]: (event: MessageEto) => void;


  [DefaultHubEventNames.onIssueOpening]: (event: IssueOpeningViewObject) => void
  [DefaultHubEventNames.onIssueStoping]: (event: IssueStopingViewObject) => void
  [DefaultHubEventNames.onIssueFinished]: (event: IssueFinishedViewObject) => void
  [DefaultHubEventNames.onLotterySettled]: (event: LotterySettlementViewObject) => void
  [DefaultHubEventNames.onIssueOptions]: (event: IssueOptionsViewObject) => void

}

class DefaultHub {
  private hub = 'gaming';
  private hubcontext: IHubContext<WithIndex<DefaultHubEvents>>;

  constructor() {
    this.hubcontext = new HubContext<WithIndex<DefaultHubEvents>>(this.hub);
    this.init();
  }

  init() {
    this.on(DefaultHubEventNames.reconnecting, this.onReconnecting);
    this.on(DefaultHubEventNames.reconnected, this.onReconnected);
    this.on(DefaultHubEventNames.closed, this.onClosed);
    console.log('hub init', this.hubcontext.all);
  }

  /**
   * 建立SignalR连接
   * 应该在用户认证完成后调用
   */
  connect() {
    this.hubcontext.connect();
  }

  onReconnecting() {
    console.log(this.hub, 'reconnecting');
  }

  onReconnected() {
    console.log(this.hub, 'reconnected');
  }

  onClosed() {
    console.log(this.hub, 'closed');
  }

  on<Key extends keyof DefaultHubEvents>(type: Key, handler: DefaultHubEvents[Key]): void {
    this.hubcontext.on(type, handler as Handler<DefaultHubEvents[Key]>);
  }

  send(type: string, ...args: any[]): void {
    this.hubcontext.send(type, ...args);
  }

  invoke(type: string, ...args: any[]): any {
    return this.hubcontext.invoke(type, ...args) ;
  }

}

const hub = new DefaultHub();
const EventNames = {
  ...DefaultHubEventNames
} as const;

export default { hub, EventNames };

<script setup lang="ts">
const props = defineProps<{
  icon?: string
  fill?: string
}>()

const emit = defineEmits(['click'])
</script>

<template>
  <button type="button" class="button main-button" @click="$emit('click')">
    <svg class="icon">
      <use :href="props.icon || '#icon-Settings'" :fill="props.fill || '#333'" />
    </svg>
  </button>
</template>

<style scoped>
button {
  outline: revert;
  border: none;
  margin: 0;
  padding: 0;
  width: auto;
  overflow: visible;
  text-align: inherit;
  border-radius: 0;
  background-color: unset;
  background: transparent;
  color: inherit;
  font: inherit;
  line-height: normal;
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;
  -webkit-appearance: none;
  appearance: none;
}

.button {
  background: none;
  transition: all 0.2s ease-out;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0.63rem;
  border-radius: 0.625rem;
  border: 1px solid rgba(125, 146, 255, 0.3);
  font-weight: 900;
  background: radial-gradient(42% 42% at 50.56% 100%,
      rgba(255, 238, 149, 0) 0%,
      rgba(255, 238, 149, 0) 100%);
  backdrop-filter: blur(2.5px);
  -webkit-backdrop-filter: blur(2.5px);
  background-position: center bottom;
}

.button:hover {
  cursor: pointer;
  background: radial-gradient(42% 42% at 50.56% 100%,
      rgba(125, 146, 255, 0.7) 0%,
      rgba(239, 107, 252, 0) 100%);
  border: 1px solid rgba(125, 146, 255, 0.6);
}

.icon {
  width: 1em;
  height: 1em;
}
</style>

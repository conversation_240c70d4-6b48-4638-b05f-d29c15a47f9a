<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { onClickOutside } from '@vueuse/core'
import Poker from './PokerCard.vue'
import { useGameStore } from '@/stores/game'

// 控制弹窗显隐
const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const modalRef = ref<HTMLElement | null>(null)
const isMounted = ref(false)

// 获取游戏 store
const gameStore = useGameStore()

// 适配历史记录格式
const records = computed(() => gameStore.recentDraws.map((item) => {
  const [suit, num] = item.drawNumber

  const small = ['A', '2', '3', '4', '5', '6']
  const big = ['8', '9', '10', 'J', 'Q', 'K']

  let size = ''
  if (small.includes(num)) {
    size = '小'
  } else if (big.includes(num)) {
    size = '大'
  } else if (num === '7') {
    size = '--' // 特殊处理
  }

  return {
    id: item.issueNumber,
    num,
    suit: suit.toLowerCase(),
    size
  }
}))


// 弹窗出现时设置挂载标记
watch(() => props.visible, async (val) => {
  if (val) {
    await nextTick()
    isMounted.value = true
  } else {
    setTimeout(() => {
      isMounted.value = false
    }, 300)
  }
})

// 点击外部关闭弹窗，忽略 `.main-button`
onClickOutside(modalRef, (event) => {
  const isTrigger = (event.target as HTMLElement).closest('.main-button')
  if (!isTrigger) {
    emit('update:visible', false)
  }
}, {
  ignore: ['.main-button']
})
</script>

<template>
  <transition name="fade-blur">
    <div v-show="props.visible"
         class="fixed left-1/2 top-14 transform -translate-x-1/2 inset-0 bg-black/70 w-[92%] h-[80%] flex items-end justify-center z-50 border-solid border-[#7d92ff4d] rounded-xl border shadow-lg"
         style="box-shadow: 0 0 33px #f7acff26;">
      <div ref="modalRef" class="text-white backdrop-blur-sm w-full h-full max-w-md rounded-xl p-4 relative">
        <div class="text-lg font-bold mb-3 flex justify-between items-center">
          <span>开奖记录</span>
          <button @click="emit('update:visible', false)" class="text-white text-xl">
            <svg class="w-6 h-6">
              <use href="#icon-Close" fill="#333" />
            </svg>
          </button>
        </div>

        <div v-if="records.length > 0" class="space-y-2 max-h-[60vh] overflow-y-auto pr-1">
          <div v-for="(record, index) in records" :key="index"
               class="flex justify-between items-center border-b border-solid px-3 py-2 border-[#7d92ff4d]">
            <span class="text-xs text-gray-400">#{{ record.id }}</span>
            <span class="text-xs text-gray-400">
              <Poker :value="record.num" :suit="record.suit" :revealed="true" />
            </span>
            <span>{{ record.size }}</span>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 text-sm py-6">
          暂无记录
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped>
.fade-blur-enter-active,
.fade-blur-leave-active {
  transition: opacity 0.2s ease;
}
.fade-blur-enter-from,
.fade-blur-leave-to {
  opacity: 0;
}
</style>

// 游戏相关类型定义

/**
 * 游戏玩法数据接口
 */
export interface GamePlayType {
  /** 唯一标识符，格式：lotteryTypeId:playingTypeId */
  id: string;
  /** 彩票类型ID */
  lotteryTypeId: string;
  /** 彩票类型名称 */
  lotteryTypeName: string;
  /** 玩法类型ID */
  playingTypeId: string;
  /** 玩法类型名称 */
  playingTypeName: string;
  /** 彩票游戏类型 */
  lotteryGameType: number;
  /** 玩法说明 */
  instruction: string;
  /** 显示名称 */
  displayName: string;
}

/**
 * 游戏类型枚举
 */
export enum GameType {
  /** 数字类型 */
  NUMBER = 0,
  /** 黑桃 */
  SPADES = 8,
  /** 其他花色和大小 */
  OTHER = 9
}

/**
 * 投注数据接口
 */
export interface BetData {
  /** 投注项目显示名称 */
  displayName: string;
  /** 投注金额 */
  amount: number;
  /** 游戏玩法ID */
  gameId: string;
}

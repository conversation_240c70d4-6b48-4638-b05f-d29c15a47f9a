// 游戏相关类型定义

/**
 * 游戏玩法数据接口
 * 基于后端 /lottery/games API 返回的数据结构
 */
export interface GamePlayType {
  /** 唯一标识符，格式：lotteryTypeId:playingTypeId，如 "Tbhp:Large" */
  id: string;
  /** 彩票类型ID，如 "Tbhp" */
  lotteryTypeId: string;
  /** 彩票类型名称，如 "Tron哈希扑克" */
  lotteryTypeName: string;
  /** 玩法类型ID，如 "Large", "N10", "PokerAce" 等 */
  playingTypeId: string;
  /** 玩法类型名称，如 "大", "数字10", "PokerAce" 等 */
  playingTypeName: string;
  /** 彩票游戏类型：0=数字类型, 8=黑桃, 9=其他花色和大小 */
  lotteryGameType: number;
  /** 玩法说明，如 "大", "押10", "押A" 等 */
  instruction: string;
  /** 显示名称，如 "大", "10", "A" 等 */
  displayName: string;
}

/**
 * 游戏类型枚举
 * 基于 lotteryGameType 字段的值
 */
export enum LotteryGameType {
  /** 数字类型：2-10, A, J, Q, K */
  NUMBER = 0,
  /** 黑桃 */
  SPADES = 8,
  /** 其他花色和大小：红桃、梅花、方块、大、小 */
  OTHER = 9
}

/**
 * 投注数据接口
 */
export interface BetData {
  /** 投注项目显示名称 */
  displayName: string;
  /** 投注金额 */
  amount: number;
  /** 游戏玩法ID */
  gameId: string;
}

/**
 * 投注字符串格式：displayName + 金额
 * 例如："大10", "A50", "黑桃100"
 */
export type BetString = string;

<script setup lang="ts">
// ========== 导入模块 ==========
import { onMounted, ref, watch } from 'vue'
import { useWebApp } from '@/scripts/twa' // 导入封装好的Telegram WebApp功能
import LightWheel from './components/LightWheel.vue' // 灯光轮盘组件
import HeaderBar from './components/HeaderBar.vue' // 头部导航栏组件
import TimeBar from './components/TimeBar.vue' // 时间栏组件
import LoadingOverlay from './components/ui/LoadingOverlay.vue' // 加载遮罩层组件
import SvgSprite from './components/ui/SvgSprite.vue' // SVG图标精灵组件
import Tips from './components/ui/Tips.vue' // 提示框组件
import AdvancedNotification from './components/ui/AdvancedNotification.vue' // 高级通知组件
import { useTips } from './composables/useTips' // 提示功能的组合式函数
import { useSignalR } from './composables/useSignalR' // SignalR实时通信功能
import { usePageVisibility } from './composables/usePageVisibility' // 页面可见性监听功能
import { useNetworkStatus } from './composables/useNetworkStatus' // 网络状态监听功能
import { useOrderEvents } from './composables/useOrderEvents' // 订单事件监听功能
import { ErrorType } from './scripts/services/errorHandler' // 错误类型枚举
import { useAuthStore } from '@/stores/auth' // 认证状态管理
import { updateSignalRToken } from '@/scripts/utils/socket-client' // 更新SignalR连接token的工具函数

// ========== 组合式函数初始化 ==========
// 只使用tipMessage和showTips，不使用showNotification
const { tipMessage, showTips } = useTips()

// 页面加载状态
const loading = ref(true)

// 网络状态监听
const { isOnline } = useNetworkStatus()

// 认证状态管理
const authStore = useAuthStore()

// 订单事件监听
useOrderEvents()

// ========== 高级通知相关状态 ==========
const advancedNotificationVisible = ref(false) // 高级通知显示状态
const notificationMessage = ref('') // 通知消息内容
const notificationType = ref<'info' | 'success' | 'error' | 'warning'>('info') // 通知类型
const errorType = ref<ErrorType | undefined>(undefined) // 错误类型
const isRetryable = ref(false) // 是否可重试
const errorDetail = ref('') // 错误详情

// ========== Telegram WebApp 初始化 ==========
const webApp = useWebApp() // 初始化Telegram WebApp SDK
const initData = webApp.initData // 获取Telegram初始化数据
webApp.HapticFeedback?.notificationOccurred('success') // 触发成功反馈震动
console.log('Telegram initData:', webApp.initData) // 输出初始化数据用于调试

// ========== 高级通知显示函数 ==========
/**
 * 显示高级通知
 * @param options 通知选项配置
 */
function showAdvancedNotification(options: {
  message: string, // 通知消息
  type?: 'info' | 'success' | 'error' | 'warning', // 通知类型
  errorType?: ErrorType, // 错误类型
  retryable?: boolean, // 是否可重试
  detail?: string // 错误详情
}) {
  notificationMessage.value = options.message
  notificationType.value = options.type || 'info'
  errorType.value = options.errorType
  isRetryable.value = options.retryable || false
  errorDetail.value = options.detail || ''
  advancedNotificationVisible.value = true
}

// ========== 重试操作处理函数 ==========
/**
 * 处理重试操作
 * 根据不同错误类型执行相应的重试策略
 */
function handleRetry() {
  // 根据不同错误类型执行不同重试策略
  if (errorType.value === ErrorType.NETWORK) {
    init() // 网络错误：重新初始化连接
  } else if (errorType.value === ErrorType.TIMEOUT) {
    init() // 超时错误：重新初始化连接
  } else {
    // 其他错误：通用重试
    init()
  }
}

// ========== 网络状态监听 ==========
/**
 * 监听网络状态变化
 * 当网络断开时显示错误通知，恢复时自动重连
 */
watch(isOnline, (online) => {
  if (!online) {
    // 网络断开时的处理
    showAdvancedNotification({
      message: '网络连接已断开，请检查网络设置',
      type: 'error',
      errorType: ErrorType.NETWORK,
      retryable: false,
      detail: '您的设备似乎已经断开网络连接。请检查您的网络设置，并在网络恢复后点击重试按钮。'
    })
  } else {
    // 网络恢复时的处理
    showAdvancedNotification({
      message: '网络已恢复，正在重新连接...',
      type: 'success',
      retryable: false
    })
    // 重新连接SignalR
    init()
  }
})

// ========== 认证Token监听 ==========
/**
 * 监听认证token变化
 * 当token更新时，同步更新SignalR连接的token
 */
watch(() => authStore.token?.accessToken, (newToken) => {
  if (newToken) {
    updateSignalRToken(newToken)
  }
}, { immediate: true }) // 立即执行一次

// ========== SignalR 初始化 ==========
/**
 * 初始化SignalR连接
 * 设置错误回调和成功回调
 */
const { init } = useSignalR(
  // SignalR错误消息回调函数
  (msg) => {
    // 确保这个回调能够正确处理错误消息
    console.log('SignalR 错误:', msg); // 添加日志方便调试
    showAdvancedNotification({
      message: msg,
      type: msg.includes('失败') || msg.includes('错误') ? 'error' : 'info',
      retryable: msg.includes('失败') || msg.includes('错误')
    });
  }, 
  // SignalR连接成功回调函数
  () => {
    loading.value = false; // 隐藏加载状态
  },
  initData // 传入Telegram初始化数据
);

// ========== 页面可见性监听 ==========
/**
 * 页面可见性切换处理
 * 当页面重新变为可见时，检查网络状态并重连
 */
usePageVisibility(() => {
  // 检查网络状态
  if (navigator.onLine) {
    // 网络正常时重新连接
    showAdvancedNotification({
      message: '正在重新连接...',
      type: 'info'
    })
    window.location.reload() // 刷新页面
  } else {
    // 网络异常时提示用户
    showAdvancedNotification({
      message: '网络连接已断开，请检查网络后刷新页面',
      type: 'error',
      errorType: ErrorType.NETWORK,
      retryable: false
    })
  }
})

// ========== 组件挂载时的初始化 ==========
/**
 * 组件挂载时执行初始化
 * 捕获初始化过程中的异常并显示错误通知
 */
onMounted(() => {
  try {
    init() // 初始化SignalR连接
  } catch (error) {
    console.error('初始化失败:', error)
    showAdvancedNotification({
      message: '初始化失败，请刷新页面重试',
      type: 'error',
      errorType: ErrorType.UNKNOWN,
      retryable: true,
      detail: error instanceof Error ? error.message : String(error)
    })
  }
})
</script>

<template>
  <!-- 简单提示框组件 -->
  <Tips v-model:visible="showTips" :message="tipMessage" type="success" />
  
  <!-- 高级通知组件 -->
  <AdvancedNotification 
    v-model:visible="advancedNotificationVisible"
    :message="notificationMessage"
    :type="notificationType"
    :error-type="errorType"
    :retryable="isRetryable"
    :detail="errorDetail"
    @retry="handleRetry"
  />
  
  <!-- SVG图标精灵 -->
  <SvgSprite />
  
  <!-- 主容器 -->
  <div class="_container">
    <!-- 加载遮罩层 -->
    <LoadingOverlay :visible="loading" />
    
    <!-- 主要内容区域（非加载状态时显示） -->
    <div class="table_mobile" v-if="!loading">
      <!-- 头部导航栏 -->
      <HeaderBar class="_header" />
      
      <!-- 时间栏 -->
      <TimeBar />
      
      <!-- 桌面容器 -->
      <div class="_table_container">
        <!-- 灯光轮盘组件 -->
        <LightWheel />
      </div>
    </div>
  </div>
</template>
<style scoped>
/* 框架 */
._container {
  min-height: 100vh;
  background: url(data:image/png;base64,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);
  background-repeat: repeat;
  background-position: center;
}
/* ========== 移动端桌面样式 ========== */
.table_mobile {
  min-height: 100vh; /* 最小高度为视口高度 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 垂直方向排列 */
  width: 100%; /* 宽度100% */
  user-select: none; /* 禁止文本选择 */
  -moz-user-select: none; /* 兼容Firefox */
  -webkit-user-select: none; /* 兼容Webkit内核浏览器 */
}

/* ========== 头部样式 ========== */
._header {
  margin: 0 auto; /* 水平居中 */
  position: absolute; /* 绝对定位 */
  z-index: 1001; /* 设置较高的层级 */
  left: 0; /* 左边距为0 */
  right: 0; /* 右边距为0 */
}

/* ========== 桌面容器样式 ========== */
._table_container {
  display: flex; /* 弹性布局 */
  justify-content: center; /* 水平居中 */
  position: absolute; /* 绝对定位 */
  width: 100%; /* 宽度100% */
  top: 10vh; /* 距离顶部10%视口高度 */
}
</style>
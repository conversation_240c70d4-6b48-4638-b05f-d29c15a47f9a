import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import services from '@/scripts/services/index';

interface Token {
  accessToken: string;
  refreshToken: string;
  expiresAt?: number;
  tokenType?: string;
}

const timeout = 1000 * 60 * 5; // 5 minutes
const timeoutHandle = ref<NodeJS.Timeout | null>(null);

export const useAuthStore = defineStore(
  "auth",
  () => {

    const token = ref<Token | null>(null);

    const profile = reactive<HttpApi.Identity.Dtos.ProfileDto>({} as HttpApi.Identity.Dtos.ProfileDto);

    /** is super role in static route */
    const isStaticSuper = computed(() => {
      const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

      return VITE_AUTH_ROUTE_MODE === 'static' && profile.roles.includes(VITE_STATIC_SUPER_ROLE);
    });

    /** Is login */
    const isAuthorized = computed(() => Boolean(token.value));

    const permissions = computed(() => profile.premissions || []);

    /** Refresh token */
    async function refreshTokenAsync(isImmediate = false) {
      try {
        if (!token.value?.refreshToken) {
          return false;
        }
        if (isImmediate && timeoutHandle.value !== null) {
          clearTimeout(timeoutHandle.value);
        }

        const loginResponse = await services.authorizationService.refreshAccessTokenAsync(token.value.refreshToken!);
        token.value = {
          accessToken: loginResponse.accessToken,
          refreshToken: loginResponse.refreshToken,
          expiresAt: new Date().getTime() + 7200 * 1000,
          tokenType: 'Bearer'
        };
        // Refresh token before it expires (5 minutes)
        setTimeout(refreshTokenAsync, 7200 * 1000 - timeout);
        return true;
      } catch {
        // Refresh token failed, try again in 30 seconds
        setTimeout(refreshTokenAsync, 30 * 1000);
      }
      return false;
    }

    async function getUserInfoAsync() {
      try {
        const userInfo = await services.authorizationService.getUserInfoAsync();
        Object.assign(profile, userInfo);
        return true;
      } catch {
        return false;
      }
    }

    async function signInAsync(authorizeCode: string , botId?: string) {
      const loginResponse = await services.authorizationService.signInAsync({ authorizeCode , botId })
      return loginResponse
    }

    /** Reset auth store */
    async function signOutAsync() {
      // const authStore = useAuthStore();

      // authStore.$reset();
      // authStore.token = null;
      // authStore.profile = {} as HttpApi.Identity.Dtos.ProfileDto;
      token.value = null;
      Object.assign(profile, {} as HttpApi.Identity.Dtos.ProfileDto);
    }

    /**
     * Login
     *
     * @param userName User name
     * @param password Password
     * @param [redirect=true] Whether to redirect after login. Default is `true`
     */
    async function signInSuccess(loginResponse: HttpApi.Identity.Dtos.LoginResponse, redirect = true) {
      try {
        const expiresAt = new Date().getTime() + 7200 * 1000;
        token.value = {
          accessToken: loginResponse.accessToken,
          refreshToken: loginResponse.refreshToken,
          expiresAt,
          tokenType: 'Bearer'
        };
        setTimeout(refreshTokenAsync, 7200 * 1000 - timeout);
        // const pass = await getUserInfoAsync();
      } catch {
        await signOutAsync();
      }
    }
    return {
      token,
      profile,
      permissions,
      isStaticSuper,
      isAuthorized,
      signInAsync,
      signOutAsync,
      signInSuccess,
      refreshTokenAsync,
      $reset: signOutAsync // 添加这一行来实现 $reset
    };
  },
  {
    persist: {
      pick: ['token']
    }
  }
);

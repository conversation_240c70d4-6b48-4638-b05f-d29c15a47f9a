<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { useSettingsStore } from '../../stores/settings'
import GameRulesModal from './GameRulesModal.vue'
// 接收父组件传入的 visible
const props = defineProps<{
    visible: boolean
}>()
const settings = useSettingsStore()
const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
}>()
const showRules = ref(false)
// 内部状态
// const soundEnabled = ref(true)
const menuRef = ref<HTMLElement | null>(null)
const isMenuMounted = ref(false)

// 监听 visible 变化
watch(() => props.visible, async (newValue) => {
    if (newValue) {
        // 菜单显示时，等待下一个DOM更新周期再设置isMenuMounted
        await nextTick()
        isMenuMounted.value = true
    } else {
        // 菜单隐藏时重置状态
        setTimeout(() => {
            if (!props.visible) {
                isMenuMounted.value = false
            }
        }, 300) // 等待动画结束
    }
})

// 切换声音
// const toggleSound = (event: Event) => {
//     event.stopPropagation()
//     soundEnabled.value = !soundEnabled.value
// }

// 点击菜单外部关闭菜单
onClickOutside(menuRef, (event) => {
    // 检查目标元素是否是设置按钮或其子元素
    const isSettingsButton = (event.target as HTMLElement).closest('.main-button')

    // 如果不是设置按钮，则关闭菜单
    if (!isSettingsButton) {
        emit('update:visible', false)
    }
}, { ignore: ['.main-button'] }) // 忽略设置按钮

// 菜单项点击处理
const goToRules = () => {
    showRules.value = true
    console.log('跳转到游戏规则')
    emit('update:visible', false)
}

// const goHome = () => {
//     console.log('返回首页')
//     emit('update:visible', false)
// }

// 阻止菜单上的点击事件冒泡
const handleMenuClick = (event: Event) => {
    event.stopPropagation()
}


</script>

<template>
    <div class="custom-menu-container" ref="menuRef" @click="handleMenuClick">
        <transition name="fade-scale">
            <div v-show="props.visible"
                class="menu-panel absolute right-4 mt-14 w-48 bg-[#23262B] rounded-xl border border-white/10 shadow-lg p-2 text-white z-50 gap-5">
                <!-- 声音开关 -->
                <div class="menu-item flex justify-between items-center py-2 px-2 rounded hover:bg-white/10">
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5" viewBox="0 0 24 24">
                            <g fill="none" stroke="currentColor" stroke-width="2">
                                <path
                                    d="M3.158 13.93a3.75 3.75 0 0 1 0-3.86a1.5 1.5 0 0 1 .993-.7l1.693-.339a.45.45 0 0 0 .258-.153L8.17 6.395c1.182-1.42 1.774-2.129 2.301-1.938S11 5.572 11 7.42v9.162c0 1.847 0 2.77-.528 2.962c-.527.19-1.119-.519-2.301-1.938L6.1 15.122a.45.45 0 0 0-.257-.153L4.15 14.63a1.5 1.5 0 0 1-.993-.7Z" />
                                <path stroke-linecap="round"
                                    d="M15.536 8.464a5 5 0 0 1 .027 7.044m4.094-9.165a8 8 0 0 1 .044 11.27" />
                            </g>
                        </svg>
                        <span>声音</span>
                    </div>

                    <!-- 关键：用 checkbox 做 switch，v-model 控制，不监听 click -->
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" v-model="settings.soundEnabled" />
                        <div
                            class="w-10 h-5 bg-gray-500 rounded-full peer peer-checked:bg-blue-500 transition-all duration-200">
                        </div>
                        <div
                            class="absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform duration-200 peer-checked:translate-x-5">
                        </div>
                    </label>
                </div>



                <!-- 游戏规则 -->
                <div class="menu-item flex items-center gap-2 py-2 px-2 rounded hover:bg-white/10 cursor-pointer"
                    @click="goToRules">

                    <svg fill="currentColor" class="w-4 h-4" viewBox="0 0 32 32">
                        <path d="M10 16h12v2H10zm0-6h12v2H10z" />
                        <path fill="999"
                            d="m16 30l-6.176-3.293A10.98 10.98 0 0 1 4 17V4a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2v13a10.98 10.98 0 0 1-5.824 9.707ZM6 4v13a8.99 8.99 0 0 0 4.766 7.942L16 27.733l5.234-2.79A8.99 8.99 0 0 0 26 17V4Z" />
                    </svg>
                    <span>游戏规则</span>
                </div>
                <!-- 历史记录 -->
                <!-- <div class="menu-item flex items-center gap-2 py-2 px-2 rounded hover:bg-white/10 cursor-pointer"
                    @click="goToRules">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fill="999" fill-rule="evenodd"
                            d="M16.33 3.75H6.67c-.535 0-.98 0-1.345.03c-1.411.115-2.432 1.164-2.545 2.545c-.03.365-.03.812-.03 1.345v8.66c0 .535 0 .98.03 1.345c.03.38.098.736.27 1.073a2.75 2.75 0 0 0 1.202 1.202c.337.172.693.24 1.073.27c.365.03.81.03 1.344.03h9.662c.534 0 .98 0 1.344-.03c.38-.03.736-.098 1.073-.27a.75.75 0 0 0-.68-1.336c-.091.046-.228.088-.516.111c-.295.024-.68.025-1.252.025H6.7c-.572 0-.957 0-1.253-.025c-.287-.023-.424-.065-.514-.111a1.25 1.25 0 0 1-.547-.547c-.046-.09-.088-.227-.111-.515c-.024-.295-.025-.68-.025-1.252V8.25h14.486q.012.625.014 1.25a.75.75 0 1 0 1.5 0q0-.28-.003-.558c-.007-.67-.027-1.807-.091-2.618c-.113-1.424-1.072-2.43-2.481-2.544c-.365-.03-.81-.03-1.345-.03m2.352 3c-.048-.797-.278-1.406-1.13-1.475c-.295-.024-.68-.025-1.252-.025H6.7c-.572 0-.957 0-1.253.025c-.818.067-1.163.68-1.189 1.475z"
                            clip-rule="evenodd" />
                        <path fill="999" d="M6.5 9.25a.75.75 0 0 0 0 1.5h6a.75.75 0 0 0 0-1.5z" />
                        <path fill="999" fill-rule="evenodd"
                            d="M17 10.25a3.25 3.25 0 1 0 1.706 6.017l1.264 1.263a.75.75 0 1 0 1.06-1.06l-1.263-1.264A3.25 3.25 0 0 0 17 10.25m-1.75 3.25a1.75 1.75 0 1 1 3.5 0a1.75 1.75 0 0 1-3.5 0"
                            clip-rule="evenodd" />
                        <path fill="999"
                            d="M6.5 11.25a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5zm0 3a.75.75 0 0 0 0 1.5h5a.75.75 0 0 0 0-1.5zm0 2a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5z" />
                    </svg>
                    <span>历史记录</span>
                </div> -->

                <!-- 返回首页 -->
                <!-- <div class="menu-item flex items-center gap-2 py-2 px-2 rounded hover:bg-white/10 cursor-pointer"
                    @click="goHome">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                    </svg>
                    <span>返回首页</span>
                </div> -->



                <!-- 版本号 -->
                <div
                    class="menu-item flex items-center justify-center gap-2 py-2 px-2 rounded hover:bg-white/10 cursor-pointer">
                    <div class="text-center text-xs text-gray-400">
                        v0.0.1
                    </div>
                </div>
            </div>
        </transition>
        <GameRulesModal v-model:visible="showRules" />
    </div>
</template>

<style scoped>
.custom-menu-container {
    position: relative;
    z-index: 50;
}

.menu-panel {
    transform-origin: top right;
    background: rgba(0, 0, 0, .7);
    border: 1px solid rgba(125, 146, 255, .3);
    box-shadow: 0 0 33px #f7acff26;
    backdrop-filter: blur(11.5px);
    -webkit-backdrop-filter: blur(11.5px);
    border-radius: .9375rem;
    z-index: 1001;
}

.menu-item {
    transition: background-color 0.2s ease;
}

/* 动画过渡效果 */
.fade-scale-enter-active,
.fade-scale-leave-active {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-scale-enter-from {
    transform: scale(0.95);
    opacity: 0;
}

.fade-scale-enter-to {
    transform: scale(1);
    opacity: 1;
}

.fade-scale-leave-from {
    transform: scale(1);
    opacity: 1;
}

.fade-scale-leave-to {
    transform: scale(0.95);
    opacity: 0;
}
</style>
// ========================================
// HTTP API 身份认证相关类型定义
// ========================================

/**
 * HTTP API 身份认证数据传输对象命名空间
 * 包含所有与用户认证相关的请求和响应数据结构定义
 * 
 * @namespace HttpApi.Identity.Dtos
 * @description
 * 该命名空间定义了身份认证系统中使用的所有数据传输对象(DTOs)，
 * 确保前后端数据交互的类型安全和一致性
 */
declare namespace HttpApi.Identity.Dtos {
  /**
   * 用户登录请求数据结构
   * 
   * @interface LoginRequest
   * @description
   * 定义用户登录时需要提交的数据格式，主要用于Telegram WebApp的认证流程
   * 
   * @example
   * const loginRequest: LoginRequest = {
   *   authorizeCode: "query_id=AAHdF6IQAAAAAN0XohDhrOrc&user=%7B%22id%22%3A279058397...",
   *   botId: "1234567890" // Telegram Bot的ID
   * };
   */
  interface LoginRequest {
    /**
     * 授权码
     * 
     * @type {string}
     * @description
     * 由Telegram WebApp提供的授权码，包含用户的身份验证信息
     * 通常是经过编码的查询字符串，包含用户ID、认证哈希等信息
     * 
     * @example
     * "query_id=AAHdF6IQAAAAAN0XohDhrOrc&user=%7B%22id%22%3A279058397..."
     * 
     * @required 必填字段
     * @security 该字段包含敏感信息，需要安全传输
     */
    authorizeCode: string;

    /**
     * Bot ID (可选)
     * 
     * @type {string}
     * @optional
     * @description
     * Telegram Bot的唯一标识符，用于Bot认证场景
     * 当应用需要以Bot身份进行认证时使用
     * 
     * @example
     * "1234567890"
     * 
     * @note
     * - 仅在Bot认证模式下需要提供
     * - 应与Telegram中注册的Bot ID一致
     * - 用于服务器端验证Bot的合法性
     */
    botId?: string;
  }

  /**
   * 登录响应数据结构
   * 
   * @interface LoginResponse
   * @description
   * 服务器返回的登录认证结果，包含用户会话所需的令牌信息
   * 这些令牌用于后续API请求的身份验证
   * 
   * @example
   * const loginResponse: LoginResponse = {
   *   accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
   *   refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   * };
   */
  interface LoginResponse {
    /**
     * 访问令牌
     * 
     * @type {string}
     * @description
     * 用于API请求认证的短期令牌，通常有效期较短（如1小时）
     * 需要在HTTP请求头中以 "Bearer token" 的格式携带
     * 
     * @example
     * "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
     * 
     * @security
     * - 应安全存储，避免泄露
     * - 有效期短，减少安全风险
     * - 用于所有需要认证的API请求
     * 
     * @usage
     * headers: { Authorization: `Bearer ${accessToken}` }
     */
    accessToken: string;

    /**
     * 刷新令牌
     * 
     * @type {string}
     * @description
     * 用于获取新访问令牌的长期令牌，有效期较长（如7天或30天）
     * 当访问令牌过期时，使用此令牌获取新的访问令牌
     * 
     * @example
     * "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwidHlwZSI6InJlZnJlc2giLCJpYXQiOjE1MTYyMzkwMjJ9.KhL4vKbAbKL_Jgbg6sI8m6zQDg0a0O5l-QdWe_OvQ4U"
     * 
     * @security
     * - 更加敏感，需要最高级别的安全存储
     * - 有效期长，一旦泄露影响较大
     * - 只在令牌刷新时使用
     * 
     * @note
     * - 刷新令牌的安全性比访问令牌更重要
     * - 通常存储在更安全的位置（如HttpOnly Cookie）
     * - 可能在刷新时被更新
     */
    refreshToken: string;
  }

  /**
   * 路由数据传输对象
   * 
   * @interface RouteDto
   * @description
   * 定义应用程序路由信息的数据结构，支持嵌套路由配置
   * 用于动态路由生成和权限控制
   * 
   * @example
   * const route: RouteDto = {
   *   name: "Dashboard",
   *   path: "/dashboard",
   *   redirect: "/dashboard/overview",
   *   children: [
   *     { name: "Overview", path: "/dashboard/overview" },
   *     { name: "Analytics", path: "/dashboard/analytics" }
   *   ]
   * };
   */
  interface RouteDto {
    /**
     * 路由名称
     * 
     * @type {string}
     * @description
     * 路由的唯一标识名称，用于路由导航和引用
     * 通常使用PascalCase命名规范
     * 
     * @example
     * "Dashboard", "UserProfile", "GameRoom"
     * 
     * @required 必填字段
     */
    name: string;

    /**
     * 路由路径
     * 
     * @type {string}
     * @description
     * URL路径，定义了访问该路由的具体地址
     * 遵循RESTful风格的路径设计
     * 
     * @example
     * "/dashboard", "/user/profile", "/game/room/:id"
     * 
     * @required 必填字段
     * @note
     * - 支持动态参数（如 :id）
     * - 应该以 / 开头
     * - 避免尾部斜杠以保持一致性
     */
    path: string;

    /**
     * 重定向路径 (可选)
     * 
     * @type {string}
     * @optional
     * @description
     * 当访问当前路由时，自动重定向到指定路径
     * 常用于根路由的默认重定向
     * 
     * @example
     * "/dashboard/overview" // 访问 /dashboard 时重定向到 /dashboard/overview
     * 
     * @usage
     * 适用场景：
     * - 设置默认子路由
     * - 路由别名处理
     * - 权限重定向
     */
    redirect?: string;

    /**
     * 子路由数组 (可选)
     * 
     * @type {RouteDto[]}
     * @optional
     * @description
     * 嵌套的子路由配置，支持多层路由结构
     * 实现路由的层次化管理
     * 
     * @example
     * children: [
     *   { name: "Overview", path: "/dashboard/overview" },
     *   { name: "Settings", path: "/dashboard/settings" }
     * ]
     * 
     * @recursive 支持递归嵌套
     * @note
     * - 子路由的path通常包含父路由路径
     * - 支持无限层级嵌套
     * - 用于实现复杂的页面布局结构
     */
    children?: RouteDto[];
  }

  /**
   * 用户资料数据传输对象
   * 
   * @interface ProfileDto
   * @description
   * 用户的详细信息数据结构，包含身份识别、联系方式、权限等信息
   * 用于用户资料展示、权限验证和个性化设置
   * 
   * @example
   * const profile: ProfileDto = {
   *   id: "user_123456",
   *   username: "john_doe",
   *   nickname: "John",
   *   email: "<EMAIL>",
   *   phoneNumber: "+1234567890",
   *   roles: ["user", "moderator"],
   *   avatar: "https://example.com/avatar.jpg",
   *   preferences: { theme: "dark", language: "zh-CN" }
   * };
   */
  interface ProfileDto {
    /**
     * 用户唯一标识符
     * 
     * @type {string}
     * @description
     * 用户在系统中的唯一标识，不可变且永久有效
     * 通常由系统生成，用于所有用户相关的数据关联
     * 
     * @example
     * "user_123456", "uuid-v4-string", "telegram_279058397"
     * 
     * @required 必填字段
     * @immutable 创建后不可修改
     * @unique 在系统中唯一
     */
    id: string;

    /**
     * 用户名
     * 
     * @type {string}
     * @description
     * 用户的登录账号名，通常用于登录和公开展示
     * 需要在系统中保持唯一性
     * 
     * @example
     * "john_doe", "alice_smith", "gamer_2024"
     * 
     * @required 必填字段
     * @unique 在系统中唯一
     * @constraints
     * - 通常只允许字母、数字、下划线
     * - 长度限制（如3-20字符）
     * - 不能包含敏感词汇
     */
    username: string;

    /**
     * 用户昵称/显示名称
     * 
     * @type {string}
     * @description
     * 用户的友好显示名称，用于界面展示和社交互动
     * 可以包含特殊字符和空格，不需要唯一性
     * 
     * @example
     * "John Doe", "爱丽丝", "游戏达人", "🎮 Player"
     * 
     * @required 必填字段
     * @mutable 用户可以修改
     * @display 主要用于界面显示
     */
    nickname: string;

    /**
     * 邮箱地址
     * 
     * @type {string}
     * @description
     * 用户的电子邮件地址，用于通信和账户恢复
     * 需要符合邮箱格式规范
     * 
     * @example
     * "<EMAIL>", "<EMAIL>"
     * 
     * @required 必填字段
     * @format 必须符合邮箱格式
     * @verification 通常需要邮箱验证
     * @unique 在系统中可能需要唯一
     */
    email: string;

    /**
     * 手机号码
     * 
     * @type {string}
     * @description
     * 用户的手机号码，用于短信通知和双因子认证
     * 包含国际区号的完整格式
     * 
     * @example
     * "+1234567890", "+86 138 0013 8000", "+44 20 7946 0958"
     * 
     * @required 必填字段
     * @format 包含国际区号的标准格式
     * @verification 通常需要短信验证
     * @security 敏感信息，需要保护隐私
     */
    phoneNumber: string;

    /**
     * 用户角色列表
     * 
     * @type {string[]}
     * @description
     * 用户在系统中拥有的角色集合，用于权限控制和功能访问
     * 支持多角色系统，实现细粒度的权限管理
     * 
     * @example
     * ["user"], ["user", "moderator"], ["admin", "super_admin"]
     * 
     * @required 必填字段
     * @authorization 用于权限验证
     * @multiple 支持多个角色
     * 
     * @common_roles
     * - "user": 普通用户
     * - "moderator": 版主/管理员
     * - "admin": 系统管理员
     * - "super_admin": 超级管理员
     * - "vip": VIP用户
     * - "bot": 机器人账户
     */
    roles: string[];

    /**
     * 扩展属性字段
     * 
     * @type {any}
     * @description
     * 支持任意额外的用户属性，提供灵活的扩展能力
     * 可以存储各种自定义用户数据
     * 
     * @example
     * {
     *   avatar: "https://example.com/avatar.jpg",
     *   preferences: { theme: "dark", language: "zh-CN" },
     *   gameStats: { level: 15, score: 1250 },
     *   socialLinks: { twitter: "@john_doe", github: "johndoe" },
     *   lastLoginAt: "2024-01-15T10:30:00Z",
     *   isVerified: true,
     *   subscription: { type: "premium", expiresAt: "2024-12-31" }
     * }
     * 
     * @flexible 支持任意类型的数据
     * @extensible 便于系统功能扩展
     * @optional 所有扩展字段都是可选的
     * 
     * @common_extensions
     * - avatar: 用户头像URL
     * - preferences: 用户偏好设置
     * - statistics: 用户统计数据
     * - socialLinks: 社交媒体链接
     * - gameData: 游戏相关数据
     * - subscriptions: 订阅信息
     */
    [key: string]: any;
  }
}

/**
 * HTTP API 身份认证服务接口命名空间
 * 定义了所有与用户认证相关的服务接口规范
 * 
 * @namespace HttpApi.Identity.Services
 * @description
 * 该命名空间包含身份认证系统的服务接口定义，
 * 确保服务实现的一致性和可维护性
 */
declare namespace HttpApi.Identity.Services {
  /**
   * 认证授权服务接口
   * 
   * @interface IAuthorizationService
   * @description
   * 定义用户认证授权相关操作的服务接口规范
   * 包括登录、令牌刷新、用户信息获取等核心功能
   * 
   * @example
   * class AuthorizationService implements IAuthorizationService {
   *   async signInAsync(request: Dtos.LoginRequest): Promise<Dtos.LoginResponse> {
   *     // 实现登录逻辑
   *   }
   *   // ... 其他方法实现
   * }
   * 
   * @implements 
   * 实现该接口的类需要提供所有定义的方法
   */
  interface IAuthorizationService {
    /**
     * 用户登录认证方法
     * 
     * @method signInAsync
     * @async
     * @description
     * 处理用户登录请求，验证用户身份并返回认证令牌
     * 支持多种认证方式，如密码登录、OAuth、Telegram WebApp等
     * 
     * @param {Dtos.LoginRequest} request - 登录请求数据
     * @param {string} request.authorizeCode - 授权码
     * @param {string} [request.botId] - Bot ID（可选）
     * 
     * @returns {Promise<Dtos.LoginResponse>} 登录响应结果
     * @returns {string} returns.accessToken - 访问令牌
     * @returns {string} returns.refreshToken - 刷新令牌
     * 
     * @throws {Error} 认证失败时抛出错误
     * 
     * @example
     * const response = await authService.signInAsync({
     *   authorizeCode: "telegram_auth_code",
     *   botId: "1234567890"
     * });
     * 
     * @workflow
     * 1. 验证授权码的有效性
     * 2. 验证Bot ID（如果提供）
     * 3. 生成访问令牌和刷新令牌
     * 4. 返回令牌信息
     */
    signInAsync(request: Dtos.LoginRequest): Promise<Dtos.LoginResponse>;

    /**
     * 刷新访问令牌方法
     * 
     * @method refreshAccessTokenAsync
     * @async
     * @description
     * 使用刷新令牌获取新的访问令牌，延长用户会话
     * 实现无感知的令牌更新，提升用户体验
     * 
     * @param {string} refreshToken - 刷新令牌
     * 
     * @returns {Promise<Dtos.LoginResponse>} 新的令牌信息
     * @returns {string} returns.accessToken - 新的访问令牌
     * @returns {string} returns.refreshToken - 新的刷新令牌（可能更新）
     * 
     * @throws {Error} 刷新令牌无效或过期时抛出错误
     * 
     * @example
     * const newTokens = await authService.refreshAccessTokenAsync(
     *   "current_refresh_token"
     * );
     * 
     * @security
     * - 验证刷新令牌的有效性和完整性
     * - 检查令牌是否在黑名单中
     * - 记录令牌刷新的安全日志
     * 
     * @workflow
     * 1. 验证刷新令牌的格式和签名
     * 2. 检查令牌是否过期或被撤销
     * 3. 生成新的访问令牌
     * 4. 可选：生成新的刷新令牌
     * 5. 返回新的令牌信息
     */
    refreshAccessTokenAsync(refreshToken: string): Promise<Dtos.LoginResponse>;

    /**
     * 获取用户信息方法
     * 
     * @method getUserInfoAsync
     * @async
     * @description
     * 根据当前用户的访问令牌获取用户的详细信息
     * 用于用户资料展示、权限验证和个性化配置
     * 
     * @returns {Promise<Dtos.ProfileDto>} 用户详细信息
     * @returns {string} returns.id - 用户ID
     * @returns {string} returns.username - 用户名
     * @returns {string} returns.nickname - 昵称
     * @returns {string} returns.email - 邮箱
     * @returns {string} returns.phoneNumber - 手机号
     * @returns {string[]} returns.roles - 角色列表
     * @returns {any} returns[key] - 其他扩展属性
     * 
     * @throws {Error} 访问令牌无效或用户不存在时抛出错误
     * 
     * @example
     * const userInfo = await authService.getUserInfoAsync();
     * console.log(`欢迎，${userInfo.nickname}！`);
     * 
     * @authentication
     * 该方法需要有效的访问令牌，通常通过以下方式提供：
     * - HTTP Authorization头: "Bearer {accessToken}"
     * - 请求拦截器自动添加
     * 
     * @usage_scenarios
     * - 应用启动时获取用户信息
     * - 用户资料页面数据加载
     * - 权限检查和角色验证
     * - 个性化设置的数据源
     * 
     * @workflow
     * 1. 从请求中提取访问令牌
     * 2. 验证令牌的有效性和权限
     * 3. 根据令牌中的用户ID查询用户信息
     * 4. 过滤敏感信息（如密码哈希）
     * 5. 返回用户的公开信息
     */
    getUserInfoAsync(): Promise<Dtos.ProfileDto>;
  }
}
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from './App.vue'
import TWAPlugin from './scripts/twa'
import { ErrorHandler, ErrorType } from './scripts/services/errorHandler'
import './style.css'
import './assets/main.css'

const app = createApp(App)
const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(TWAPlugin) // ✅ 插件在同一个 app 实例上注册
app.mount('#app') // ✅ 只 mount 一次

// 全局通知类型
interface NotificationOptions {
  message: string
  type: string
  errorType?: ErrorType
  retryable?: boolean
  detail?: string
}
type NotificationService = (options: NotificationOptions) => void

// 注入全局通知
app.config.globalProperties.$notification = ((options: NotificationOptions): void => {
  window.dispatchEvent(new CustomEvent('app:notification', { detail: options }))
}) as NotificationService

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('全局错误:', err)
  const errorDetail = ErrorHandler.handle(err)
  app.config.globalProperties.$notification({
    message: errorDetail.message,
    type: 'error',
    errorType: errorDetail.type,
    retryable: errorDetail.retry,
    detail: errorDetail.detail
  })
}

// 捕获未处理的 Promise 错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise错误:', event.reason)
  const errorDetail = ErrorHandler.handle(event.reason)
  app.config.globalProperties.$notification({
    message: errorDetail.message,
    type: 'error',
    errorType: errorDetail.type,
    retryable: errorDetail.retry,
    detail: errorDetail.detail
  })
})

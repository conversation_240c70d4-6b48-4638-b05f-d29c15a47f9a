<script setup lang="ts">
import { ref, watch } from 'vue'
import { ErrorType } from '@/scripts/services/errorHandler'

const props = defineProps<{
  visible: boolean
  message: string
  type: 'info' | 'success' | 'error' | 'warning'
  errorType?: ErrorType
  retryable?: boolean
  detail?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', val: boolean): void
  (e: 'retry'): void
}>()

const expanded = ref(false)

function toggleDetail() {
  expanded.value = !expanded.value
}

watch(() => props.visible, (val) => {
  if (val) {
    const timeout = props.type === 'error' ? 10000 : 3000 // 错误提示显示更长时间
    setTimeout(() => {
      emit('update:visible', false)
    }, timeout)
  }
})
</script>

<template>
  <transition name="fade-slide">
    <div
      v-if="visible"
      class="fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-3 rounded-lg shadow-lg z-[1500] max-w-md "
      :class="{
        'bg-blue-500/10 text-white': type === 'info',
        'bg-green-500/10 text-white': type === 'success',
        'bg-red-500/10 text-white': type === 'error',
        'bg-yellow-500/10 text-white': type === 'warning'
      }"
    >
      <div class="flex items-start">
        <div v-if="type === 'error'" class="mr-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="flex-1">
          <div class="text-[12px] font-medium">{{ message }}</div>
          <div v-if="detail && expanded" class="mt-1 text-[12px] opacity-90">
            {{ detail }}
          </div>
        </div>
        <div class="flex space-x-2">
          <button 
            v-if="detail" 
            @click="toggleDetail" 
            class="text-xs underline opacity-90 hover:opacity-100"
          >
            {{ expanded ? '收起' : '详情' }}
          </button>
          <button 
            v-if="retryable" 
            @click="emit('retry')" 
            class="text-xs bg-white/30 hover:bg-white/40 px-2 py-0.5 rounded"
          >
            重试
          </button>
          <button 
            @click="emit('update:visible', false)" 
            class="text-xs opacity-70 hover:opacity-100"
          >
            ✕
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>
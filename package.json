{"name": "light-wheel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "@tailwindcss/vite": "^4.1.4", "@twa-dev/sdk": "^8.0.2", "@twa-dev/types": "^8.0.2", "@vueuse/core": "^13.1.0", "axios": "1.7.7", "axios-retry": "4.5.0", "gsap": "^3.12.7", "nanoid": "5.0.8", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qs": "6.13.0", "vue": "^3.5.13"}, "devDependencies": {"@types/node": "22.9.0", "@types/qs": "6.9.17", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.15", "tailwindcss": "^4.1.4", "typescript": "~5.7.2", "vite": "^6.3.1", "vue-tsc": "^2.2.8"}}
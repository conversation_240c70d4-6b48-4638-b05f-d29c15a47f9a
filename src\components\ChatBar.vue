
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import AnimatedTextList from './ui/AnimatedTextList.vue'
import { useNotificationStore } from '@/stores/notification'

const listRef = ref<InstanceType<typeof AnimatedTextList>>()
const notificationStore = useNotificationStore()

const randomNames = ['XX', 'XXX', 'XXXXX', 'YY', 'ZZZZ']

let timer: number | null = null

// 添加一条随机消息
const addNewMessage = () => {
  const randomName = randomNames[Math.floor(Math.random() * randomNames.length)]
  listRef.value?.addItem(`${randomName} Bet 500`)
}

// 添加通知消息
const addNotificationMessage = (message: string, type: string) => {
  // 根据消息类型添加不同的前缀图标
  let prefix = ''
  switch (type) {
    case 'success':
      prefix = '✅ '
      break
    case 'error':
      prefix = '❌ '
      break
    case 'warning':
      prefix = '⚠️ '
      break
    case 'info':
    default:
      prefix = 'ℹ️ '
      break
  }

  listRef.value?.addItem(`${prefix}${message}`)
}

// 监听通知 store 的变化
watch(() => [notificationStore.show, notificationStore.message, notificationStore.type],
  ([show, message, type]) => {
    if (show && message && typeof message === 'string' && typeof type === 'string') {
      console.log('📢 [ChatBar] 显示通知:', message, type)
      addNotificationMessage(message, type)

      // 显示通知后，可以选择自动隐藏通知状态
      // 这样避免重复显示相同的通知
      setTimeout(() => {
        notificationStore.hideNotification()
      }, 1000)
    }
  }
)

onMounted(() => {
  // 页面加载后开始定时添加随机消息（可选，用于测试）
  timer = window.setInterval(() => {
    addNewMessage()
  }, 10000) // 每10秒添加一次，降低频率避免干扰通知
})

onUnmounted(() => {
  // 页面卸载时清除定时器
  if (timer !== null) {
    clearInterval(timer)
  }
})
</script>
<template>

    <div class=" fixed inset-x-0 bottom-[60px]   flex items-center justify-between px-4 z-40">

        <!-- <div class="_container gap-1">
            <div class="_sit_number flex items-center justify-center">
                <img src="../assets/usdt.svg" alt="USDT" class="w-4 h-4" />
            </div>
            <div class="_sit_label flex items-center justify-center">50000.00</div>
            <div class="_sit_backdrop flex items-center justify-center">
                <img src="../assets/plus.svg" alt="USDT" class="w-4 h-4" />
            </div>
        </div> -->
        <AnimatedTextList ref="listRef" class="w-full" />
        <!-- <button @click="addNewMessage" class="bg-blue-500 text-white px-4 py-2 rounded">
            添加一条记录
        </button> -->
        <!-- <div class="flex justify-center items-center gap-4">
           <div class="flex justify-center flex-col items-start gap-0">
            <span class="text-white text-xs">XXX Bet 500</span>
            <span class="text-white text-xs">XX Bet 500</span>
            <span class="text-white text-xs">XXXXX Bet 500</span>
            <span class="text-white text-xs">XX Bet 500</span>
            <span class="text-white text-xs">XXXX Bet 500</span>
           </div>
        </div> -->
    </div>
</template>
<style scoped>
/* .layout {
    width: 100%;
    backdrop-filter: blur(4px);
} */
/* ._container {
    border: .06em solid #7d92ff;
    box-sizing: content-box;
    border-radius: .63em;
    padding: .31em .62em;
    height: 1.88em;
    position: relative;
    top: -3.5em;
    right: 0em;
    overflow: hidden;
    display: flex;
    justify-content: center;
    --current-color: white;
} */

/* ._sit_number {
    position: absolute;
    top: 50%;
    left: .3125em;
    transform: translateY(-55%);
} */

/* ._sit_label {
    text-align: center;
    align-self: center;
    width: 100%;
    display: flex;
    gap: .4375em;
    justify-content: center;
    align-items: center;
    font-size: .875em;
} */
/* ._sit_backdrop {
    position: absolute;
    top: 50%;
    right: .3125em;
    transform: translateY(-55%);
} */
/* ._sit_backdrop_1r3kw_1134 {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
} */
</style>
<template>
  <div class="game-betting">
    <!-- 大小投注区域 -->
    <div class="betting-section">
      <h3 class="section-title">大小</h3>
      <div class="betting-grid size-grid">
        <button
          v-for="game in gameStore.sizeGameTypes"
          :key="game.id"
          class="bet-button size-button"
          :class="{ active: selectedBets[game.displayName] }"
          @click="toggleBet(game)"
        >
          <span class="bet-name">{{ game.displayName }}</span>
          <span class="bet-instruction">{{ game.instruction }}</span>
        </button>
      </div>
    </div>

    <!-- 花色投注区域 -->
    <div class="betting-section">
      <h3 class="section-title">花色</h3>
      <div class="betting-grid suit-grid">
        <button
          v-for="game in gameStore.allSuitGameTypes"
          :key="game.id"
          class="bet-button suit-button"
          :class="{ 
            active: selectedBets[game.displayName],
            spades: game.playingTypeId === 'PokerSpades',
            hearts: game.playingTypeId === 'PokerHearts',
            clubs: game.playingTypeId === 'PokerClubs',
            diamonds: game.playingTypeId === 'PokerDiamonds'
          }"
          @click="toggleBet(game)"
        >
          <span class="bet-name">{{ game.displayName }}</span>
          <span class="bet-instruction">{{ game.instruction }}</span>
        </button>
      </div>
    </div>

    <!-- 数字投注区域 -->
    <div class="betting-section">
      <h3 class="section-title">数字</h3>
      <div class="betting-grid number-grid">
        <button
          v-for="game in gameStore.numberGameTypes"
          :key="game.id"
          class="bet-button number-button"
          :class="{ active: selectedBets[game.displayName] }"
          @click="toggleBet(game)"
        >
          <span class="bet-name">{{ game.displayName }}</span>
        </button>
      </div>
    </div>

    <!-- 投注金额选择 -->
    <div class="amount-section" v-if="hasSelectedBets">
      <h3 class="section-title">选择金额</h3>
      <div class="amount-grid">
        <button
          v-for="amount in betAmounts"
          :key="amount"
          class="amount-button"
          :class="{ active: selectedAmount === amount }"
          @click="selectedAmount = amount"
        >
          {{ amount }}
        </button>
      </div>
    </div>

    <!-- 已选投注显示 -->
    <div class="selected-bets" v-if="hasSelectedBets">
      <h3 class="section-title">已选投注</h3>
      <div class="selected-list">
        <div
          v-for="(bet, name) in selectedBets"
          :key="name"
          class="selected-item"
        >
          <span class="bet-display">{{ name }}</span>
          <button class="remove-bet" @click="removeBet(name)">×</button>
        </div>
      </div>
    </div>

    <!-- 投注按钮 -->
    <div class="action-section" v-if="hasSelectedBets && selectedAmount > 0">
      <button class="place-bet-button" @click="placeBets">
        投注 ({{ Object.keys(selectedBets).length }} 项 × {{ selectedAmount }} = {{ totalAmount }})
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGameStore } from '@/stores/game'
import type { GamePlayType } from '@/types/game'

const gameStore = useGameStore()

// 已选择的投注项目
const selectedBets = ref<Record<string, GamePlayType>>({})

// 选择的投注金额
const selectedAmount = ref(0)

// 可选投注金额
const betAmounts = [10, 50, 100, 500, 1000]

// 计算属性
const hasSelectedBets = computed(() => Object.keys(selectedBets.value).length > 0)
const totalAmount = computed(() => Object.keys(selectedBets.value).length * selectedAmount.value)

// 切换投注选择
const toggleBet = (game: GamePlayType) => {
  if (selectedBets.value[game.displayName]) {
    delete selectedBets.value[game.displayName]
  } else {
    selectedBets.value[game.displayName] = game
  }
}

// 移除投注
const removeBet = (name: string) => {
  delete selectedBets.value[name]
}

// 提交投注
const placeBets = () => {
  const bets = Object.entries(selectedBets.value).map(([name, game]) => ({
    displayName: name,
    amount: selectedAmount.value,
    gameId: game.id
  }))

  console.log('🎯 提交投注:', bets)
  
  // 构造投注字符串格式：displayName + 金额
  const betStrings = bets.map(bet => `${bet.displayName}${bet.amount}`)
  console.log('🎯 投注字符串:', betStrings)

  // TODO: 这里调用实际的投注API
  alert(`投注成功！\n投注项目：${betStrings.join(', ')}`)
  
  // 清空选择
  selectedBets.value = {}
  selectedAmount.value = 0
}
</script>

<style scoped>
.game-betting {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.betting-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.betting-grid {
  display: grid;
  gap: 10px;
}

.size-grid {
  grid-template-columns: repeat(2, 1fr);
}

.suit-grid {
  grid-template-columns: repeat(2, 1fr);
}

.number-grid {
  grid-template-columns: repeat(4, 1fr);
}

.bet-button {
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.bet-button:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.bet-button.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.bet-name {
  font-size: 16px;
  font-weight: bold;
}

.bet-instruction {
  font-size: 12px;
  opacity: 0.8;
}

/* 花色特殊样式 */
.suit-button.spades { border-color: #000; }
.suit-button.hearts { border-color: #dc3545; }
.suit-button.clubs { border-color: #28a745; }
.suit-button.diamonds { border-color: #fd7e14; }

.amount-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.amount-button {
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.amount-button:hover {
  border-color: #28a745;
}

.amount-button.active {
  border-color: #28a745;
  background: #28a745;
  color: white;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background: #e9ecef;
  border-radius: 20px;
  font-size: 14px;
}

.remove-bet {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.place-bet-button {
  width: 100%;
  padding: 15px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s;
}

.place-bet-button:hover {
  background: #218838;
}
</style>

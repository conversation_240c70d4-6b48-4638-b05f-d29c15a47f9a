<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { onClickOutside } from '@vueuse/core'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const modalRef = ref<HTMLElement | null>(null)
const isMounted = ref(false)

watch(() => props.visible, async (val) => {
  if (val) {
    await nextTick()
    isMounted.value = true
  } else {
    setTimeout(() => {
      if (!props.visible) isMounted.value = false
    }, 300)
  }
})

onClickOutside(modalRef, () => emit('update:visible', false))
</script>

<template>
  <transition name="fade-scale">
    <div
      v-show="props.visible"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start  justify-center"
    >
      <div
        ref="modalRef"
        class="w-full max-w-md  text-white rounded-xl  p-6 relative mt-10"
      >
        <!-- 头部 -->
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-bold flex items-center gap-2">
            <svg fill="currentColor" class="w-5 h-5" viewBox="0 0 32 32">
                        <path d="M10 16h12v2H10zm0-6h12v2H10z" />
                        <path fill="999"
                            d="m16 30l-6.176-3.293A10.98 10.98 0 0 1 4 17V4a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2v13a10.98 10.98 0 0 1-5.824 9.707ZM6 4v13a8.99 8.99 0 0 0 4.766 7.942L16 27.733l5.234-2.79A8.99 8.99 0 0 0 26 17V4Z" />
                    </svg>游戏规则</h2>
          <button @click="emit('update:visible', false)" class="text-white hover:opacity-70">
            <svg class="w-6 h-6"><use href="#icon-Close" /></svg>
          </button>
        </div>

        <!-- 正文内容 -->
        <div class="text-sm leading-relaxed space-y-2 max-h-[60vh] overflow-y-auto pr-1">
          <p>1. 游戏玩法：大小，花色，牌点。</p>
          <p>2. 每轮结束后揭示一张牌，根据牌点决定胜负。</p>
          <p>3. 大小玩法： A~6 为“小”，8~K 为“大”， “7”既不是小也不是大。</p>
          <p>4. 牌点玩法：A-K选择扑克牌点进行游戏共13张牌。</p>
          <p>5. 花色玩法：根据牌点花色进行投注，共4个花色，黑红梅芳。</p>
          <p>6. 根据Tron网络区块链进行开奖确认，20个区块高度为一期。</p>
          <p>7. 开奖结果采用区块哈希的值进行算数运算得到牌点，可验证开奖结果，公平公正。</p>
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped>
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: all 0.25s ease;
}
.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.95);
}
.fade-scale-enter-to {
  opacity: 1;
  transform: scale(1);
}
.fade-scale-leave-from {
  opacity: 1;
  transform: scale(1);
}
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>

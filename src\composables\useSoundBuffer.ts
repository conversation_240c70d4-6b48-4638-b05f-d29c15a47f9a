// src/composables/useSoundBuffer.ts
import { useSettingsStore } from '../stores/settings'

const audioContext = new AudioContext()
const bufferCache = new Map<string, AudioBuffer>()

export const useSoundBuffer = () => {
  const settings = useSettingsStore()

  const loadBuffer = async (file: string): Promise<AudioBuffer> => {
    if (bufferCache.has(file)) {
      return bufferCache.get(file)!
    }

    const response = await fetch(`/assets/${file}.webm`)
    const arrayBuffer = await response.arrayBuffer()
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
    bufferCache.set(file, audioBuffer)
    return audioBuffer
  }

  const play = async (file: string, volume = 1) => {
    if (!settings.soundEnabled) return

    const buffer = await loadBuffer(file)
    const source = audioContext.createBufferSource()
    const gainNode = audioContext.createGain()

    gainNode.gain.value = volume
    source.buffer = buffer
    source.connect(gainNode)
    gainNode.connect(audioContext.destination)

    source.start(0)
  }

  return {
    play
  }
}

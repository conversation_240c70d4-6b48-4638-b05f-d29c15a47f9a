// ========================================
// SignalR Hub 上下文管理模块
// 提供基于事件的实时通信封装
// ========================================

// ========== 依赖导入 ==========
import socketClient from '@/scripts/utils/socket-client'; // 导入Socket客户端工具

// ========== 类型定义 ==========

/**
 * 事件方法名类型
 * 支持字符串或Symbol作为事件标识符
 * 
 * @type {string | symbol}
 * @description
 * 定义事件名称的合法类型，提供灵活的事件命名方式
 * - string: 常用的字符串事件名
 * - symbol: 提供唯一性保证的Symbol事件名
 */
export type Methods = string | symbol;

/**
 * 事件处理器函数类型
 * 
 * @template T 事件数据的类型，默认为unknown
 * @param {T} event 事件参数，包含事件相关数据
 * @returns {void} 不返回任何值
 * 
 * @description
 * 定义标准的事件处理函数签名：
 * - 接收一个可选的事件参数
 * - 不应该返回值（副作用函数）
 * - 用于处理特定类型的事件
 * 
 * @example
 * const userJoinHandler: Handler<{ userId: string, username: string }> = (event) => {
 *   console.log(`用户 ${event.username} 加入了游戏`);
 * };
 */
export type Handler<T = unknown> = (event: T) => void;

/**
 * 通配符事件处理器函数类型
 * 能够处理所有类型的事件
 * 
 * @template T 事件映射对象类型，默认为Record<string, unknown>
 * @param {keyof T} type 事件类型名称
 * @param {T[keyof T]} event 对应类型的事件数据
 * @returns {void} 不返回任何值
 * 
 * @description
 * 通配符处理器的特点：
 * - 能够监听所有事件类型
 * - 接收事件类型和事件数据两个参数
 * - 常用于日志记录、调试、通用处理等场景
 * 
 * @example
 * const wildcardHandler: WildcardHandler<GameEvents> = (type, event) => {
 *   console.log(`收到事件 ${String(type)}:`, event);
 * };
 */
export type WildcardHandler<T = Record<string, unknown>> = (type: keyof T, event: T[keyof T]) => void;

/**
 * 特定事件类型的处理器列表
 * 
 * @template T 事件数据类型，默认为unknown
 * @description
 * 存储同一事件类型的所有处理器函数
 * 使用数组形式支持多个处理器订阅同一事件
 * 
 * @example
 * const userJoinHandlers: EventHandlerList<UserJoinEvent> = [
 *   handler1,
 *   handler2,
 *   handler3
 * ];
 */
export type EventHandlerList<T = unknown> = Array<Handler<T>>;

/**
 * 通配符事件处理器列表
 * 
 * @template T 事件映射对象类型
 * @description
 * 存储所有通配符事件处理器
 * 这些处理器会对所有事件类型进行响应
 */
export type WildCardEventHandlerList<T = Record<string, unknown>> = Array<WildcardHandler<T>>;

/**
 * 事件处理器映射表
 * 
 * @template Events 事件映射接口，定义了事件名到事件数据的映射关系
 * @description
 * 核心的事件管理数据结构：
 * - Key: 事件类型名或通配符'*'
 * - Value: 对应的处理器列表
 * 
 * @structure
 * Map<事件名 | '*', 处理器列表>
 * 
 * @example
 * // 事件映射示例
 * interface GameEvents {
 *   'user-join': { userId: string, username: string };
 *   'user-leave': { userId: string };
 *   'game-start': { roomId: string };
 * }
 * 
 * // 对应的处理器映射
 * const eventMap: EventHandlerMap<GameEvents> = new Map([
 *   ['user-join', [userJoinHandler1, userJoinHandler2]],
 *   ['user-leave', [userLeaveHandler]],
 *   ['*', [wildcardHandler]] // 通配符处理器
 * ]);
 */
export type EventHandlerMap<Events extends Record<Methods, unknown>> = Map<
  keyof Events | '*',
  EventHandlerList<Events[keyof Events]> | WildCardEventHandlerList<Events>
>;

// ========== 接口定义 ==========

/**
 * Hub上下文接口
 * 定义实时通信Hub的标准操作接口
 * 
 * @interface IHubContext
 * @template Events 事件映射类型，定义Hub支持的所有事件
 * 
 * @description
 * 该接口抽象了SignalR Hub的核心功能：
 * - 事件订阅和取消订阅
 * - 事件发送和调用
 * - 事件处理器管理
 * 
 * @design_pattern Observer Pattern (观察者模式)
 * 支持多个观察者(处理器)订阅同一个事件
 */
export interface IHubContext<Events extends Record<Methods, unknown>> {
  /**
   * 所有事件处理器的映射表
   *
   * @property all
   * @type {EventHandlerMap<Events>}
   * @description
   * 存储当前Hub上下文中所有已注册的事件处理器
   * 用于事件分发、处理器管理和状态查询
   */
  all: EventHandlerMap<Events>;

  /**
   * 建立Socket连接
   *
   * @method connect
   * @description
   * 手动建立与SignalR Hub的连接
   * 应该在用户认证完成后调用此方法
   */
  connect(): void;

  /**
   * 订阅事件方法
   * 
   * @method on
   * @template Key 事件类型，必须是Events中定义的键
   * @param {Key} type 要监听的事件类型
   * @param {Handler<Events[Key]>} handler 事件处理函数
   * @returns {void}
   * 
   * @description
   * 注册事件监听器，当指定事件触发时调用处理函数
   * 支持同一事件注册多个处理器
   * 
   * @example
   * hubContext.on('user-join', (event) => {
   *   console.log(`用户 ${event.username} 加入`);
   * });
   */
  on: <Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>) => void;

  /**
   * 取消事件订阅方法
   * 
   * @method off
   * @template Key 事件类型
   * @param {Key} type 要取消监听的事件类型
   * @param {Handler<Events[Key]>} [handler] 可选的特定处理函数，如果不提供则移除该事件的所有处理器
   * @returns {void}
   * 
   * @description
   * 移除事件监听器：
   * - 提供handler参数：移除特定的处理器
   * - 不提供handler参数：移除该事件类型的所有处理器
   * 
   * @example
   * // 移除特定处理器
   * hubContext.off('user-join', specificHandler);
   * 
   * // 移除所有user-join事件的处理器
   * hubContext.off('user-join');
   */
  off: <Key extends keyof Events>(type: Key, handler?: Handler<Events[Key]>) => void;

  /**
   * 发送事件方法（单向通信）
   * 
   * @method send
   * @param {string} event 事件名称
   * @param {...any[]} args 事件参数
   * @returns {void}
   * 
   * @description
   * 向服务器发送事件消息，不等待响应
   * 适用于单向数据传输场景，如状态更新、通知等
   * 
   * @example
   * // 发送用户动作事件
   * hubContext.send('user-action', { action: 'bet', amount: 100 });
   */
  send: (event: string, ...args: any[]) => void;

  /**
   * 调用服务器方法（双向通信）
   * 
   * @method invoke
   * @param {string} event 要调用的服务器方法名
   * @param {...any[]} args 方法参数
   * @returns {any} 服务器返回的结果
   * 
   * @description
   * 调用服务器端的方法并等待返回结果
   * 适用于需要响应的操作，如查询数据、执行命令等
   * 
   * @example
   * // 调用服务器方法获取游戏状态
   * const gameState = await hubContext.invoke('GetGameState', roomId);
   */
  invoke: (event: string, ...args: any[]) => any;
}

// ========== 实现类 ==========

/**
 * Hub上下文实现类
 * SignalR Hub的事件管理和通信封装
 * 
 * @class HubContext
 * @template Events 事件映射类型
 * @implements {IHubContext<Events>}
 * 
 * @description
 * 该类提供了完整的SignalR Hub通信功能：
 * 1. 自动连接管理
 * 2. 事件订阅和分发
 * 3. 双向通信支持
 * 4. 类型安全的事件处理
 * 
 * @example
 * interface GameEvents {
 *   'user-join': { userId: string, username: string };
 *   'game-start': { roomId: string, players: string[] };
 * }
 * 
 * const gameHub = new HubContext<GameEvents>('gameHub');
 * 
 * gameHub.on('user-join', (event) => {
 *   console.log(`${event.username} 加入了游戏`);
 * });
 */
export class HubContext<Events extends Record<Methods, unknown>> implements IHubContext<Events> {
  /**
   * Hub名称标识
   * 
   * @property hub
   * @type {string}
   * @description
   * SignalR Hub的名称，用于标识特定的通信Hub
   * 对应服务器端的Hub类名或路由
   * 
   * @example
   * 'gameHub', 'chatHub', 'notificationHub'
   */
  public hub: string;

  /**
   * 事件处理器映射表
   * 
   * @property all
   * @type {EventHandlerMap<Events>}
   * @description
   * 存储当前Hub实例的所有事件处理器
   * 实现IHubContext接口的核心属性
   */
  public all: EventHandlerMap<Events>;

  /**
   * Hub上下文构造函数
   * 
   * @constructor
   * @param {string} hub Hub名称，对应服务器端的Hub标识
   * @param {EventHandlerMap<Events>} [all] 可选的初始事件处理器映射
   * 
   * @description
   * 初始化Hub上下文实例：
   * 1. 设置Hub名称
   * 2. 初始化事件处理器映射表
   * 3. 建立与服务器的Socket连接
   * 
   * @example
   * // 创建游戏Hub上下文
   * const gameHub = new HubContext<GameEvents>('gameHub');
   * 
   * // 使用预定义的处理器映射
   * const existingHandlers = new Map();
   * const chatHub = new HubContext<ChatEvents>('chatHub', existingHandlers);
   */
  constructor(hub: string, all?: EventHandlerMap<Events>) {
    this.hub = hub;
    this.all = all || new Map();
    // 不再自动建立Socket连接，需要手动调用connect()方法
  }

  /**
   * 建立Socket连接
   *
   * @method connect
   * @description
   * 手动建立与SignalR Hub的连接
   * 应该在用户认证完成后调用此方法
   *
   * @example
   * const gameHub = new HubContext<GameEvents>('gameHub');
   * // 等待用户认证完成
   * await authenticateUser();
   * // 建立连接
   * gameHub.connect();
   */
  connect() {
    socketClient.connect(this.hub);
  }

  /**
   * 触发事件的私有方法
   * 
   * @private
   * @method emit
   * @template Key 事件类型
   * @param {Key} type 要触发的事件类型
   * @param {Events[Key]} [event] 可选的事件数据
   * @returns {void}
   * 
   * @description
   * 内部事件分发机制：
   * 1. 首先调用该事件类型的特定处理器
   * 2. 然后调用通配符('*')处理器
   * 3. 使用slice()创建处理器数组副本，避免处理过程中修改原数组导致的问题
   * 
   * @event_flow
   * 事件分发流程：
   * 收到事件 → 查找特定处理器 → 执行特定处理器 → 查找通配符处理器 → 执行通配符处理器
   * 
   * @safety_considerations
   * - 使用slice()避免迭代过程中数组被修改
   * - 类型断言确保处理器类型正确
   * - 支持空事件数据的处理
   */
  private emit<Key extends keyof Events>(type: Key, event?: Events[Key]) {
    // 获取特定事件类型的处理器
    let handlers = this.all.get(type);
    if (handlers) {
      // 创建副本并逐个调用处理器，避免并发修改问题
      (handlers as EventHandlerList<Events[keyof Events]>).slice().forEach(handler => handler(event!));
    }

    // 获取并调用通配符处理器
    handlers = this.all.get('*');
    if (handlers) {
      (handlers as WildCardEventHandlerList<Events>).slice().forEach(handler => handler(type, event!));
    }
  }

  /**
   * 注册事件处理器
   * 
   * @public
   * @method on
   * @template Key 事件类型，必须是Events接口的键
   * @param {Key} type 要监听的事件类型
   * @param {Handler<Events[Key]> | WildcardHandler<Events>} handler 事件处理函数
   * @returns {void}
   * 
   * @description
   * 事件订阅的核心方法：
   * 1. 检查是否已有该类型的处理器列表
   * 2. 如果有，将新处理器添加到现有列表
   * 3. 如果没有，创建新的处理器列表
   * 4. 向Socket客户端注册事件监听
   * 
   * @registration_flow
   * 注册流程：
   * 用户调用on() → 添加到内部处理器列表 → 注册Socket事件监听 → 服务器事件触发 → Socket回调 → emit()分发 → 用户处理器执行
   * 
   * @example
   * // 注册用户加入事件处理器
   * gameHub.on('user-join', (event) => {
   *   updatePlayerList(event.userId, event.username);
   *   showNotification(`${event.username} 加入了游戏`);
   * });
   * 
   * // 注册通配符处理器（如果支持）
   * gameHub.on('*', (type, event) => {
   *   console.log(`事件 ${String(type)} 被触发:`, event);
   * });
   */
  on<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]> | WildcardHandler<Events>) {
    // 获取现有的处理器列表
    const handlers = this.all.get(type) as Array<Handler<Events[keyof Events]> | WildcardHandler<Events>> | undefined;
    
    if (handlers) {
      // 如果已有处理器列表，直接添加新处理器
      handlers.push(handler as Handler<Events[keyof Events]>);
      return;
    }
    
    // 创建新的处理器列表
    this.all.set(type, [handler] as EventHandlerList<Events[keyof Events]>);
    
    // 向Socket客户端注册事件监听
    // 当服务器触发事件时，通过emit方法分发到所有处理器
    socketClient.on(this.hub, type as string, (event: Events[keyof Events]) => {
      this.emit(type, event);
    });
  }

  /**
   * 移除事件处理器
   * 
   * @public
   * @method off
   * @template Key 事件类型
   * @param {Key} type 要移除监听的事件类型
   * @param {Handler<Events[Key]> | WildcardHandler<Events>} [handler] 可选的特定处理器
   * @returns {void}
   * 
   * @description
   * 事件取消订阅方法：
   * 1. 如果指定了handler，只移除该特定处理器
   * 2. 如果未指定handler，移除该事件类型的所有处理器
   * 3. 当处理器列表为空时，取消Socket事件监听
   * 
   * @bug_fix_note
   * 注意：当前代码存在逻辑错误
   * ```typescript
   * if (handlers) {
   *   return; // 这里应该是 if (!handlers)
   * }
   * ```
   * 
   * @corrected_logic
   * 正确的逻辑应该是：
   * ```typescript
   * if (!handlers) {
   *   return; // 如果没有处理器，直接返回
   * }
   * ```
   * 
   * @example
   * // 移除特定的处理器
   * gameHub.off('user-join', specificHandler);
   * 
   * // 移除所有user-join事件的处理器
   * gameHub.off('user-join');
   * 
   * @cleanup_strategy
   * 清理策略：
   * - 精确移除：只移除指定的处理器函数
   * - 批量移除：清空特定事件类型的所有处理器
   * - 资源清理：无处理器时取消Socket监听，避免内存泄漏
   */
  off<Key extends keyof Events>(type: Key, handler?: Handler<Events[Key]> | WildcardHandler<Events>) {
    const handlers = this.all.get(type) as Array<Handler<Events[keyof Events]> | WildcardHandler<Events>> | undefined;
    
    // BUG: 这里的逻辑是错误的，应该是 !handlers
    if (handlers) {
      return;
    }
    
    // 根据是否提供handler参数决定移除策略
    if (handler) {
      // 移除特定的处理器
      handlers!.splice(handlers!.indexOf(handler as Handler<Events[keyof Events]>), 1);
    } else {
      // 清空所有处理器
      this.all.set(type, []);
    }

    // 如果没有剩余的处理器，取消Socket事件监听
    if (handlers!.length === 0) {
      socketClient.off(this.hub, type as string, handler as Handler<Events[Key]>);
    }
  }

  /**
   * 发送事件到服务器（单向通信）
   * 
   * @public
   * @method send
   * @param {string} event 事件名称
   * @param {...any[]} args 事件参数列表
   * @returns {void}
   * 
   * @description
   * 向服务器发送事件消息，不等待响应
   * 适用于状态同步、通知等单向数据传输场景
   * 
   * @use_cases
   * - 玩家动作通知（下注、弃牌等）
   * - 状态更新（位置变化、状态改变）
   * - 心跳包发送
   * - 日志上报
   * 
   * @example
   * // 发送玩家下注事件
   * gameHub.send('player-bet', { 
   *   playerId: 'player123', 
   *   amount: 100, 
   *   timestamp: Date.now() 
   * });
   * 
   * // 发送心跳包
   * gameHub.send('heartbeat');
   * 
   * @performance
   * - 异步发送，不阻塞UI
   * - 适合高频次的状态同步
   * - 网络开销较低
   */
  send(event: string, ...args: any[]) {
    socketClient.send(this.hub, event, ...args);
  }

  /**
   * 调用服务器方法（双向通信）
   * 
   * @public
   * @method invoke
   * @param {string} event 要调用的服务器方法名
   * @param {...any[]} args 方法参数列表
   * @returns {any} 服务器返回的结果
   * 
   * @description
   * 调用服务器端方法并等待返回结果
   * 支持Promise异步调用，适用于需要响应的操作
   * 
   * @use_cases
   * - 数据查询（获取游戏状态、用户信息）
   * - 业务操作（创建房间、加入游戏）
   * - 验证操作（检查权限、验证输入）
   * - 配置获取（获取游戏配置、系统设置）
   * 
   * @example
   * // 异步调用示例
   * const gameState = await gameHub.invoke('GetGameState', roomId);
   * console.log('当前游戏状态:', gameState);
   * 
   * // 创建游戏房间
   * const roomInfo = await gameHub.invoke('CreateRoom', {
   *   name: '新游戏房间',
   *   maxPlayers: 6,
   *   betLimit: 1000
   * });
   * 
   * // 错误处理
   * try {
   *   const result = await gameHub.invoke('JoinRoom', roomId);
   * } catch (error) {
   *   console.error('加入房间失败:', error);
   * }
   * 
   * @error_handling
   * - 支持异步错误处理
   * - 网络超时自动处理
   * - 服务器异常传播到客户端
   * 
   * @performance_considerations
   * - 等待服务器响应，有网络延迟
   * - 适合低频次的重要操作
   * - 支持请求超时和重试机制
   */
  invoke(event: string, ...args: any[]) {
    return socketClient.invoke(this.hub, event, ...args);
  }
}
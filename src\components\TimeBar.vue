<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useGameStore } from '@/stores/game'
import Poker from './ui/PokerCard.vue'
import LastPoker from './ui/LastPoker.vue'
import CountdownTimer from './ui/CountdownTimer.vue'

const gameStore = useGameStore()

// 上一轮结果（静态翻开）
const previousCard = ref<{ suit: string, value: string } | null>(null)
// 当前等待开奖的牌（背面，旋转动画）
const waitingCard = ref<{ suit: string, value: string } | null>(null)
// 是否显示等待开奖的牌
const showWaitingCard = ref(false)
// 是否正在开奖动画中
const isDrawing = ref(false)
// LastPoker 组件的结果
const lastPokerResult = ref<{ value: string, suit: string } | null>(null)

const handleFinished = () => {
  console.log('倒计时结束！')
}

// 提取花色与点数
function extractCard(drawNumber: string[]): { suit: string; value: string } {
  const [suitRaw, value] = drawNumber
  return { suit: suitRaw.toLowerCase(), value }
}

// 监听 LightWheel 动画状态变化
const handleLightWheelAnimationEnd = (event: CustomEvent) => {
  console.log('🎯 [TimeBar] LightWheel 动画结束，开始翻牌', event.detail)

  if (waitingCard.value && gameStore.result.length === 2) {
    // 翻开等待中的牌
    const card = extractCard(gameStore.result)
    waitingCard.value = card
    isDrawing.value = false

    console.log('🃏 [TimeBar] 翻开牌:', card)

    // 2秒后将当前牌移到历史位置，并显示新的背面牌
    setTimeout(() => {
      console.log('📚 [TimeBar] 将牌移到历史位置，显示新的背面牌')
      previousCard.value = card

      // 🎯 将结果传递给 LastPoker 组件
      lastPokerResult.value = {
        value: card.value,
        suit: card.suit.toUpperCase() // 确保花色格式正确
      }
      console.log('🃏 [TimeBar] 传递结果给 LastPoker:', lastPokerResult.value)

      // 立即显示新的背面牌，等待下一次开奖
      waitingCard.value = { suit: '', value: '' } // 新的背面牌
      isDrawing.value = false
    }, 2000)
  }
}

// 监听游戏状态变化
watch(() => gameStore.phase, (phase) => {
  console.log('🎮 [TimeBar] 游戏状态变化:', phase)

  if (phase === 'opening') {
    // 新一期开始，确保显示等待开奖的背面牌
    if (!showWaitingCard.value) {
      showWaitingCard.value = true
      waitingCard.value = { suit: '', value: '' } // 背面牌
    }
    isDrawing.value = false
  } else if (phase === 'finished') {
    // 开奖阶段，开始动画
    isDrawing.value = true
    // LightWheel 动画开始，等待动画结束事件
  }
})

// 组件初始化
onMounted(() => {
  console.log('🔧 [TimeBar] 组件初始化，显示背面牌并注册事件监听器')
  showWaitingCard.value = true
  waitingCard.value = { suit: '', value: '' } // 背面牌
  window.addEventListener('lightWheelAnimationEnd', handleLightWheelAnimationEnd as EventListener)
})

// 移除事件监听器
onUnmounted(() => {
  console.log('🧹 [TimeBar] 移除事件监听器')
  window.removeEventListener('lightWheelAnimationEnd', handleLightWheelAnimationEnd as EventListener)
})

watch(
  () => gameStore.latestDraw,
  (latest) => {
    console.log(gameStore.latestDraw,'最新一期');
    if (latest?.drawNumber?.length === 2) {
      previousCard.value = extractCard(latest.drawNumber)
    }
  },
  { immediate: true }
)

</script>

<template>
  <div class="fixed top-[60px] left-0 w-full h-[60px] flex items-center justify-between gap-2 px-4 z-40">
    <div class="flex items-center justify-center gap-2">
    
  
      <!-- 当前等待开奖的牌 - 始终显示 -->
      <Poker
        :value="waitingCard?.value || ''"
        :suit="waitingCard?.suit || ''"
        :revealed="!isDrawing && waitingCard?.value !== ''"
        :class="{
          'waiting-card': isDrawing,
          'revealing-card': !isDrawing && waitingCard?.value !== ''
        }"
      />
      <!-- 上一轮开奖结果 -->
      <LastPoker
        v-if="lastPokerResult"
        :value="lastPokerResult.value"
        :suit="lastPokerResult.suit"
      />
      
    </div>
    <div>
      <CountdownTimer @finished="handleFinished" />
    </div>
  </div>
</template>

<style scoped>
/* 等待开奖的牌 - 旋转动画 */
.waiting-card {
  animation: cardRotate 2s linear infinite;
  transform-origin: center;
}

/* 翻牌动画 */
.revealing-card {
  animation: cardReveal 1s ease-in-out;
}

/* 旋转动画 */
@keyframes cardRotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

/* 翻牌动画 */
@keyframes cardReveal {
  0% {
    transform: rotateY(0deg) scale(1);
  }
  50% {
    transform: rotateY(90deg) scale(1.1);
  }
  100% {
    transform: rotateY(180deg) scale(1);
  }
}

/* 历史牌样式 */
.previous-card {
  opacity: 0.8;
  transform: scale(0.9);
}
</style>

<script setup lang="ts">
import { ref, watch,onMounted  } from 'vue'
import { useGameStore } from '@/stores/game'
import Poker from './ui/PokerCard.vue'
import CountdownTimer from './ui/CountdownTimer.vue'

const gameStore = useGameStore()

// 当前开奖展示（翻转中）
const currentCard = ref<{ suit: string, value: string } | null>(null)
// 上一轮结果（静态翻开）
const previousCard = ref<{ suit: string, value: string } | null>(null)

const handleFinished = () => {
  console.log('倒计时结束！')
}

// 提取花色与点数
function extractCard(drawNumber: string[]): { suit: string; value: string } {
  const [suitRaw, value] = drawNumber
  return { suit: suitRaw.toLowerCase(), value }
}

// 监听 phase 控制开奖展示逻辑
watch(() => gameStore.phase, (phase) => {
  if (phase === 'opening') {
    gameStore.revealed = false
    currentCard.value = null
  }
})

// revealed === true 时展示翻拍动画
watch(() => gameStore.revealed, (revealed) => {



  if (revealed && gameStore.result.length === 2) {
    const card = extractCard(gameStore.result)

    currentCard.value = card

    setTimeout(() => {
      previousCard.value = card
      currentCard.value = null
    }, 1000)
  }
})

watch(
  () => gameStore.latestDraw,
  (latest) => {
    console.log(gameStore.latestDraw,'最新一期');
    if (latest?.drawNumber?.length === 2) {
      previousCard.value = extractCard(latest.drawNumber)
    }
  },
  { immediate: true }
)

</script>

<template>
  <div class="fixed top-[60px] left-0 w-full h-[60px] flex items-center justify-between gap-2 px-4 z-40">
    <div class="flex items-center justify-center gap-2">
      <!-- 上一轮开奖结果 -->
      <Poker
        :value="previousCard?.value || ''"
        :suit="previousCard?.suit || ''"
        :revealed="true"
      />
      <!-- 当前轮翻拍 -->
      <Poker
        :value="currentCard?.value || ''"
        :suit="currentCard?.suit || ''"
        :revealed="!!currentCard"
      />
    </div>
    <div>
      <CountdownTimer @finished="handleFinished" />
    </div>
  </div>
</template>

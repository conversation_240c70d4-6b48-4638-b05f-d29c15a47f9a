// ========================================
// HTTP 客户端工具类
// 基于 Axios 封装的企业级 HTTP 请求解决方案
// 
// 主要功能：
// - 统一的请求/响应处理
// - 自动重试机制
// - 请求取消管理
// - 错误处理和状态管理
// - 类型安全的响应数据
// - 支持多种响应格式
// ========================================

import { stringify } from 'qs';
import { AxiosError, AxiosInstance, AxiosRequestConfig, CreateAxiosDefaults, type AxiosHeaderValue, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios';
import axios from 'axios';
import axiosRetry, { IAxiosRetryConfig } from 'axios-retry';
import { nanoid } from 'nanoid';

// ========== 常量定义 ==========
/** 请求ID标识符 */
const REQUEST_ID_KEY = 'X-Request-Id';

/** 后端错误代码标识 */
const BACKEND_ERROR_CODE = 'BACKEND_ERROR';

// ========== 工具函数 ==========

/**
 * 获取请求内容类型
 * @param config - Axios请求配置
 * @returns 内容类型字符串
 */
export function getContentType(config: InternalAxiosRequestConfig) {
  const contentType: AxiosHeaderValue = config.headers?.['Content-Type'] || 'application/json';
  return contentType;
}

/**
 * 检查HTTP状态码是否为成功
 * @param status - HTTP状态码
 * @returns 是否成功
 */
export function isHttpSuccess(status: number) {
  const isSuccessCode = status >= 200 && status < 300;
  return isSuccessCode || status === 304;
}

/**
 * 检查响应是否为JSON格式
 * @param response - Axios响应对象
 * @returns 是否为JSON
 */
export function isResponseJson(response: AxiosResponse) {
  const { responseType } = response.config;
  return responseType === 'json' || responseType === undefined;
}

/**
 * 创建默认请求选项
 * @param options - 部分请求选项
 * @returns 完整的请求选项
 */
export function createDefaultOptions<ResponseData = any>(options?: Partial<RequestOption<ResponseData>>) {
  const opts: RequestOption<ResponseData> = {
    onRequest: async config => config,
    isBackendSuccess: _response => true,
    onBackendFail: async () => {},
    transformBackendResponse: async response => response.data,
    onError: async () => {}
  };

  Object.assign(opts, options);
  return opts;
}

/**
 * 创建重试配置选项
 * @param config - 部分Axios配置
 * @returns 重试配置
 */
export function createRetryOptions(config?: Partial<CreateAxiosDefaults>) {
  const retryConfig: IAxiosRetryConfig = {
    retries: 0
  };

  Object.assign(retryConfig, config);
  return retryConfig;
}

/**
 * 创建Axios配置
 * @param config - 部分Axios配置
 * @returns 完整的Axios配置
 */
export function createAxiosConfig(config?: Partial<CreateAxiosDefaults>) {
  const TEN_SECONDS = 10 * 1000;

  const axiosConfig: CreateAxiosDefaults = {
    timeout: TEN_SECONDS,
    headers: {
      'Content-Type': 'application/json'
    },
    validateStatus: isHttpSuccess,
    paramsSerializer: params => {
      return stringify(params);
    }
  };

  Object.assign(axiosConfig, config);
  return axiosConfig;
}

// ========== 类型定义 ==========

/** 后端服务响应数据结构 */
export type HttpResponse<T = unknown, E = Error> = {
  /** 是否为未授权请求 */
  unAuthorizedRequest: boolean;
  /** 请求是否成功 */
  success: boolean;
  /** 响应数据 */
  result: T;
  /** 错误信息 */
  error: E;
};

/** 内容类型枚举 */
export type ContentType =
  | 'text/html'
  | 'text/plain'
  | 'multipart/form-data'
  | 'application/json'
  | 'application/x-www-form-urlencoded'
  | 'application/octet-stream';

/** 请求选项接口 */
export interface RequestOption<ResponseData = any> {
  /** 请求前的钩子函数 - 可用于添加token等 */
  onRequest: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
  /** 检查后端响应是否成功的钩子函数 */
  isBackendSuccess: (response: AxiosResponse<ResponseData>) => boolean;
  /** 后端请求失败后的钩子函数 - 可用于处理token过期等 */
  onBackendFail: (
    response: AxiosResponse<ResponseData>,
    instance: AxiosInstance
  ) => Promise<AxiosResponse | null> | Promise<void>;
  /** 当响应类型为json时，转换后端响应数据 */
  transformBackendResponse(response: AxiosResponse<ResponseData>): any | Promise<any>;
  /** 错误处理钩子函数 - 可用于显示错误消息 */
  onError: (error: AxiosError<ResponseData>) => void | Promise<void>;
}

/** 响应类型映射 */
interface ResponseMap {
  blob: Blob;
  text: string;
  arrayBuffer: ArrayBuffer;
  stream: ReadableStream<Uint8Array>;
  document: Document;
}

/** 响应类型 */
export type ResponseType = keyof ResponseMap | 'json';

/** 映射类型 */
export type MappedType<R extends ResponseType, JsonType = any> = R extends keyof ResponseMap
  ? ResponseMap[R]
  : JsonType;

/** 自定义Axios请求配置 */
export type CustomAxiosRequestConfig<R extends ResponseType = 'json'> = Omit<AxiosRequestConfig, 'responseType'> & {
  responseType?: R;
};

/** 请求实例通用接口 */
export interface RequestInstanceCommon<T> {
  /** 根据请求ID取消请求 */
  cancelRequest: (requestId: string) => void;
  /** 取消所有请求 */
  cancelAllRequest: () => void;
  /** 请求实例的自定义状态 */
  state: T;
}

/** 标准请求实例接口 */
export interface RequestInstance<S = Record<string, unknown>> extends RequestInstanceCommon<S> {
  <T = any, R extends ResponseType = 'json'>(config: CustomAxiosRequestConfig<R>): Promise<MappedType<R, T>>;
}

/** 扁平化响应成功数据 */
export type FlatResponseSuccessData<T = any, ResponseData = any> = {
  data: T;
  error: null;
  response: AxiosResponse<ResponseData>;
};

/** 扁平化响应失败数据 */
export type FlatResponseFailData<ResponseData = any> = {
  data: null;
  error: AxiosError<ResponseData>;
  response: AxiosResponse<ResponseData>;
};

/** 扁平化响应数据 */
export type FlatResponseData<T = any, ResponseData = any> =
  | FlatResponseSuccessData<T, ResponseData>
  | FlatResponseFailData<ResponseData>;

/** 扁平化请求实例接口 */
export interface FlatRequestInstance<S = Record<string, unknown>, ResponseData = any> extends RequestInstanceCommon<S> {
  <T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig<R>
  ): Promise<FlatResponseData<MappedType<R, T>, ResponseData>>;
}

// ========== 核心函数 ==========

/**
 * 创建通用请求实例
 * @param axiosConfig - Axios配置
 * @param options - 请求选项
 * @returns 请求实例相关对象
 */
function createCommonRequest<ResponseData = any>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>
) {
  const opts = createDefaultOptions<ResponseData>(options);

  const axiosConf = createAxiosConfig(axiosConfig);
  const instance = axios.create(axiosConf);

  const abortControllerMap = new Map<string, AbortController>();

  // 配置axios重试
  const retryOptions = createRetryOptions(axiosConf);
  axiosRetry(instance, retryOptions);

  // 请求拦截器
  instance.interceptors.request.use(conf => {
    const config: InternalAxiosRequestConfig = { ...conf };

    // 设置请求ID
    const requestId = nanoid();
    config.headers.set(REQUEST_ID_KEY, requestId);

    // 配置请求取消控制器
    if (!config.signal) {
      const abortController = new AbortController();
      config.signal = abortController.signal;
      abortControllerMap.set(requestId, abortController);
    }

    // 通过钩子处理配置
    const handledConfig = opts.onRequest?.(config) || config;

    return handledConfig;
  });

  // 响应拦截器
  instance.interceptors.response.use(
    async response => {
      const responseType: ResponseType = (response.config?.responseType as ResponseType) || 'json';

      if (responseType !== 'json' || opts.isBackendSuccess(response)) {
        return Promise.resolve(response);
      }

      const fail = await opts.onBackendFail(response, instance);
      if (fail) {
        return fail;
      }

      const backendError = new AxiosError<ResponseData>(
        'the backend request error',
        BACKEND_ERROR_CODE,
        response.config,
        response.request,
        response
      );

      await opts.onError(backendError);

      return Promise.reject(backendError);
    },
    async (error: AxiosError<ResponseData>) => {
      await opts.onError(error);

      return Promise.reject(error);
    }
  );

  /**
   * 取消指定请求
   * @param requestId - 请求ID
   */
  function cancelRequest(requestId: string) {
    const abortController = abortControllerMap.get(requestId);
    if (abortController) {
      abortController.abort();
      abortControllerMap.delete(requestId);
    }
  }

  /**
   * 取消所有请求
   */
  function cancelAllRequest() {
    abortControllerMap.forEach(abortController => {
      abortController.abort();
    });
    abortControllerMap.clear();
  }

  return {
    instance,
    opts,
    cancelRequest,
    cancelAllRequest
  };
}

/**
 * 创建标准请求实例
 * @param axiosConfig - axios配置
 * @param options - 请求选项
 * @returns 请求实例
 */
export function createRequest<ResponseData = any, State = Record<string, unknown>>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>
) {
  const { instance, opts, cancelRequest, cancelAllRequest } = createCommonRequest<ResponseData>(axiosConfig, options);

  const request: RequestInstance<State> = async function request<T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig
  ) {
    const response: AxiosResponse<ResponseData> = await instance(config);

    const responseType = response.config?.responseType || 'json';

    if (responseType === 'json') {
      return opts.transformBackendResponse(response);
    }

    return response.data as MappedType<R, T>;
  } as RequestInstance<State>;

  request.cancelRequest = cancelRequest;
  request.cancelAllRequest = cancelAllRequest;
  request.state = {} as State;

  return request;
}

/**
 * 创建扁平化请求实例
 * 响应数据格式: { data: any, error: AxiosError }
 * @param axiosConfig - axios配置
 * @param options - 请求选项
 * @returns 扁平化请求实例
 */
export function createFlatRequest<ResponseData = any, State = Record<string, unknown>>(
  axiosConfig?: CreateAxiosDefaults,
  options?: Partial<RequestOption<ResponseData>>
) {
  const { instance, opts, cancelRequest, cancelAllRequest } = createCommonRequest<ResponseData>(axiosConfig, options);

  const flatRequest: FlatRequestInstance<State, ResponseData> = async function flatRequest<
    T = any,
    R extends ResponseType = 'json'
  >(config: CustomAxiosRequestConfig) {
    try {
      const response: AxiosResponse<ResponseData> = await instance(config);

      const responseType = response.config?.responseType || 'json';

      if (responseType === 'json') {
        const data = opts.transformBackendResponse(response);

        return { data, error: null, response };
      }

      return { data: response.data as MappedType<R, T>, error: null };
    } catch (error) {
      return { data: null, error, response: (error as AxiosError<ResponseData>).response };
    }
  } as FlatRequestInstance<State, ResponseData>;

  flatRequest.cancelRequest = cancelRequest;
  flatRequest.cancelAllRequest = cancelAllRequest;
  flatRequest.state = {} as State;

  return flatRequest;
}

export { BACKEND_ERROR_CODE, REQUEST_ID_KEY };
export type { CreateAxiosDefaults, AxiosError };
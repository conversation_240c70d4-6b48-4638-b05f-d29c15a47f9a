<script setup lang="ts">
import { computed, watch, ref } from 'vue'

interface Props {
  value: string
  suit: string // 支持格式如 "SH", "SD", "SS", "SC"
}

const props = defineProps<Props>()

// 动画控制
const isAnimating = ref(false)

// 从后端 suit 字符串中提取最后一个字符（H/D/S/C）
const normalizedSuit = computed(() => props.suit.slice(-1).toUpperCase())

// 花色图标映射
const cardSuitIcon = computed(() => {
  const suitMap: Record<string, string> = {
    S: '#icon-Spades',
    H: '#icon-Hearts',
    D: '#icon-Diamonds',
    C: '#icon-Clubs',
  }
  return suitMap[normalizedSuit.value] ?? ''
})

// 花色颜色（红/黑）
const suitColor = computed(() => {
  return normalizedSuit.value === 'S' || normalizedSuit.value === 'C' ? '#000000' : '#d40000'
})

// 初始化状态
const isInitialized = ref(false)

// 监听数据变化，触发动画
watch([() => props.value, () => props.suit], (newVal, oldVal) => {
  if (!isInitialized.value) {
    // 首次初始化，直接显示，不播放动画
    isInitialized.value = true
    isAnimating.value = false
  } else {
    // 数据变化时播放滑入动画
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, 600)
  }
}, { immediate: true })
</script>

<template>
  <div class="card-wrapper">
    <div 
      class="card-container" 
      :class="{ 'slide-in': isAnimating, 'initialized': isInitialized }"
      data-type="card"
    >
      <!-- 扑克牌正面 -->
      <div 
        class="card card-front" 
        :data-role="'card-layout'" 
        :data-value="value" 
        :data-suit="suit" 
        :style="{ color: suitColor }"
      >
        <span class="card-value">{{ value }}</span>
        <svg class="card-suit-icon">
          <use :href="cardSuitIcon"></use>
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
.card-wrapper {
  padding: 2px;
  border: 2px dashed #ffffff;
  border-radius: 0.375rem;
  background: transparent;
  display: inline-block;
  overflow: hidden;
}

.card-container {
  width: 2.625em;
  height: 3.375em;
  max-width: 2.625em;
  max-height: 3.375em;
  position: relative;
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.6s ease;
}

.card-container.initialized {
  transform: translateX(0);
  opacity: 1;
}

.card-container.slide-in {
  animation: slideInFromLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.card {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 0.225rem;
  display: flex;
  user-select: none;
  pointer-events: none;
  filter: drop-shadow(-1px 0px 4px rgba(0, 0, 0, 0.39));
}

.card-front {
  background: #fff;
  color: #000;
}

.card-value {
  position: absolute;
  top: -0.09375em;
  left: 0.25em;
  font-size: 1.5em;
  font-family: 'Roboto Condensed', sans-serif;
  font-weight: 500;
  letter-spacing: -0.1em;
  line-height: 1.3;
}

.card-suit-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.375em;
  height: 2.375em;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ✅ 4K 显示器（≥2560px） */
@media (min-width: 2560px) {
  .card-wrapper {
    padding: 4px;
    border-width: 3px;
  }
  
  .card-container {
    width: 4.2em;
    height: 5.4em;
  }

  .card-value {
    font-size: 2.5em;
    height: auto;
    width: auto;
  }

  .card-suit-icon {
    width: 3.5em;
    height: 3.5em;
    transform: translate(-50%, -50%);
  }
}

/* ✅ 2K 显示器（1440px ～ 2559px） */
@media (min-width: 1440px) and (max-width: 2559px) {
  .card-wrapper {
    padding: 3px;
  }
  
  .card-container {
    width: 3.2em;
    height: 4.125em;
  }

  .card-value {
    font-size: 1.25em;
  }

  .card-suit-icon {
    width: 1.25em;
    height: 1.25em;
    transform: translate(-50%, 0%);
  }
}

/* ✅ 1080p 常规显示器（1024px ～ 1439px） */
@media (min-width: 1024px) and (max-width: 1439px) {
  .card-container {
    width: 2.625em;
    height: 3.375em;
  }

  .card-value {
    font-size: 1.5em;
  }

  .card-suit-icon {
    width: 1.475em;
    height: 1.475em;
    transform: translate(-50%, -5%);
  }
}

/* ✅ 平板设备（768px ～ 1023px） */
@media (min-width: 768px) and (max-width: 1023px) {
  .card-container {
    width: 2.2em;
    height: 2.875em;
  }

  .card-value {
    font-size: 1.2em;
  }

  .card-suit-icon {
    width: 1.375em;
    height: 1.375em;
    transform: translate(-50%, -20%);
  }
}

/* ✅ 小屏手机（≤768px） */
@media (max-width: 768px) {
  .card-container {
    width: 2.1875em;
    height: 2.875em;
  }

  .card-value {
    font-size: 1.078125em;
    height: 1.3125em;
    width: 1.375em;
  }

  .card-suit-icon {
    width: 1.375em;
    height: 1.375em;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -20%);
  }
}

/* ✅ 超小屏手机（≤480px） */
@media (max-width: 480px) {
  .card-container {
    width: 1.75em;
    height: 2.3em;
  }

  .card-value {
    font-size: 0.95em;
  }

  .card-suit-icon {
    width: 0.95em;
    height: 0.95em;
    transform: translate(-50%, -15%);
  }
}
</style>
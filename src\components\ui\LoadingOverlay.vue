<template>
    <transition name="fade">
      <div v-if="visible" class="loading-overlay">
        <div class="spinner" />
      </div>
    </transition>
  </template>
  
  <script setup lang="ts">
  const props = defineProps<{
    visible: boolean
  }>()
  </script>
  
  <style scoped>
  .loading-overlay {
    position: fixed;
    inset: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
  }
  
  .spinner {
    width: 48px;
    height: 48px;
    border: 5px solid #e5e7eb3a;
    border-top: 5px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  .fade-enter-from, .fade-leave-to {
    opacity: 0;
  }
  </style>
  
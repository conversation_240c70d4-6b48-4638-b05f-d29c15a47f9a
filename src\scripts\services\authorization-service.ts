// ========== 导入依赖模块 ==========
import { RequestInstanceState } from "../request/type"; // 请求实例状态类型
import { FlatRequestInstance, HttpResponse } from "../utils/http-client"; // HTTP客户端相关类型

/**
 * 用户余额数据接口
 */
interface BalanceData {
  /** 货币类型 */
  currencyId: string;
  /** 余额 */
  balance: number;
}

/**
 * 认证授权服务类
 * 提供用户登录、Token刷新、用户信息获取等认证相关的API服务
 * 
 * @description
 * 该服务类封装了与用户认证授权相关的所有HTTP请求操作，包括：
 * 1. 用户登录认证
 * 2. 访问令牌刷新
 * 3. 用户信息获取
 * 
 * @version 1.0.0
 * @since 1.0.0
 * 
 * @example
 * // 创建认证服务实例
 * const authService = new AuthorizationService(request);
 * 
 * // 用户登录
 * const loginResponse = await authService.signInAsync({
 *   botId: 'your-bot-id',
 *   authorizeCode: 'your-auth-code'
 * });
 * 
 * // 刷新Token
 * const refreshResponse = await authService.refreshAccessTokenAsync('refresh-token');
 * 
 * // 获取用户信息
 * const userInfo = await authService.getUserInfoAsync();
 * 
 * @class AuthorizationService
 * @implements {HttpApi.Identity.Services.IAuthorizationService}
 * @export default
 * 
 * @see FlatRequestInstance - HTTP请求实例
 * @see RequestInstanceState - 请求状态管理
 * @see HttpResponse - HTTP响应类型
 * @see HttpApi.Identity.Services.IAuthorizationService - 认证服务接口
 */
export default class AuthorizationService implements HttpApi.Identity.Services.IAuthorizationService {
  /**
   * API基础路径
   * 所有认证相关的API请求都基于此路径
   * 
   * @private
   * @readonly
   * @type {string}
   */
  private baseUrl = `/api/authorize`;

  /**
   * HTTP请求实例
   * 用于发送HTTP请求，包含请求状态管理和响应处理
   * 
   * @private
   * @type {FlatRequestInstance<RequestInstanceState, HttpResponse<unknown>>}
   * @description
   * 该实例已经配置了：
   * - 自动token处理
   * - 错误重试机制
   * - 响应数据转换
   * - 错误消息管理
   */
  private request: FlatRequestInstance<RequestInstanceState, HttpResponse<unknown>>;

  /**
   * 认证服务构造函数
   * 初始化认证服务实例，注入HTTP请求依赖
   * 
   * @constructor
   * @version 1.0.0
   * @since 1.0.0
   * 
   * @param {FlatRequestInstance<RequestInstanceState, HttpResponse<unknown>>} request - HTTP请求实例
   * 
   * @example
   * // 创建认证服务
   * const authService = new AuthorizationService(request);
   * 
   * @description
   * 构造函数采用依赖注入模式，将HTTP请求实例作为参数传入
   * 这样设计的好处：
   * 1. 便于单元测试时mock HTTP请求
   * 2. 可以复用同一个请求实例的配置
   * 3. 确保所有API请求使用统一的错误处理和认证逻辑
   * 
   * @see FlatRequestInstance - HTTP请求实例类型
   * @see RequestInstanceState - 请求状态管理类型
   * @see HttpResponse - HTTP响应类型
   */
  constructor(request: FlatRequestInstance<RequestInstanceState, HttpResponse<unknown>>) {
    this.request = request;
  }

  /**
   * 用户登录认证
   * 通过bot ID和授权码进行用户身份验证，获取访问令牌
   * 
   * @async
   * @method signInAsync
   * @version 1.0.0
   * @since 1.0.0
   * 
   * @param {HttpApi.Identity.Dtos.LoginRequest} request - 登录请求参数
   * @param {string} request.botId - Bot的唯一标识符
   * @param {string} request.authorizeCode - 授权码
   * 
   * @returns {Promise<HttpApi.Identity.Dtos.LoginResponse>} 登录响应结果
   * @returns {string} returns.accessToken - 访问令牌
   * @returns {string} returns.refreshToken - 刷新令牌
   * @returns {string} returns.tokenType - 令牌类型 (通常为"Bearer")
   * @returns {number} returns.expiresIn - 令牌过期时间(秒)
   * 
   * @throws {Error} 当登录失败时抛出错误
   * 
   * @example
   * // 执行用户登录
   * try {
   *   const loginResponse = await authService.signInAsync({
   *     botId: '1234567890',
   *     authorizeCode: 'auth-code-from-telegram'
   *   });
   *   
   *   console.log('登录成功:', loginResponse.accessToken);
   * } catch (error) {
   *   console.error('登录失败:', error.message);
   * }
   * 
   * @description
   * 该方法实现用户登录流程：
   * 1. 构造登录API的完整URL，包含bot ID和时间戳防缓存
   * 2. 发送POST请求，将授权码作为请求体
   * 3. 设置正确的Content-Type头
   * 4. 返回包含token信息的登录响应
   * 
   * @note
   * - URL中的时间戳(_t参数)用于防止浏览器缓存
   * - 授权码通常来自Telegram WebApp的初始化数据
   * - 返回的token信息会被自动存储到认证状态管理中
   */
  async signInAsync(request: HttpApi.Identity.Dtos.LoginRequest): Promise<HttpApi.Identity.Dtos.LoginResponse> {
    const response = await this.request({
      url: `${this.baseUrl}/token/${request.botId}?_t=${Date.now()}`, // 添加时间戳防止缓存
      method: 'post',
      data: request.authorizeCode, // 将授权码作为请求体发送
      headers: {
        'Content-Type': 'application/json', // 指定请求内容类型
      },
    });
    
    // 调试日志：输出响应结果（生产环境应移除）
    console.log('登录响应结果:', response);
    
    // 返回响应数据，断言data不为空
    return response.data!;
  }

  /**
   * 刷新访问令牌
   * 使用刷新令牌获取新的访问令牌，延长用户会话时间
   * 
   * @async
   * @method refreshAccessTokenAsync
   * @version 1.0.0
   * @since 1.0.0
   * 
   * @param {string} refreshToken - 刷新令牌
   * 
   * @returns {Promise<HttpApi.Identity.Dtos.LoginResponse>} 刷新后的登录响应
   * @returns {string} returns.accessToken - 新的访问令牌
   * @returns {string} returns.refreshToken - 新的刷新令牌(可能更新)
   * @returns {string} returns.tokenType - 令牌类型
   * @returns {number} returns.expiresIn - 新令牌的过期时间
   * 
   * @throws {Error} 当刷新令牌无效或过期时抛出错误
   * 
   * @example
   * // 刷新访问令牌
   * try {
   *   const refreshResponse = await authService.refreshAccessTokenAsync(
   *     'your-refresh-token'
   *   );
   *   
   *   console.log('Token刷新成功:', refreshResponse.accessToken);
   * } catch (error) {
   *   console.error('Token刷新失败:', error.message);
   *   // 通常需要重新登录
   * }
   * 
   * @description
   * 令牌刷新的重要性：
   * 1. 延长用户会话，无需重新登录
   * 2. 提高安全性，访问令牌有较短的有效期
   * 3. 改善用户体验，自动处理令牌过期
   * 
   * @note
   * - 刷新令牌通常有更长的有效期
   * - 刷新成功后，旧的访问令牌立即失效
   * - 某些情况下刷新令牌也会更新
   * - 如果刷新失败，通常需要用户重新登录
   */
  async refreshAccessTokenAsync(refreshToken: string): Promise<HttpApi.Identity.Dtos.LoginResponse> {
    const { data } = await this.request({
      url: `${this.baseUrl}/refresh`, // Token刷新专用端点
      method: 'post',
      data: refreshToken, // 将刷新令牌作为请求体发送
    });
    
    // 返回新的token信息
    return data;
  }

  /**
   * 获取当前用户信息
   * 根据当前的访问令牌获取用户的详细信息
   * 
   * @async
   * @method getUserInfoAsync
   * @version 1.0.0
   * @since 1.0.0
   * 
   * @returns {Promise<HttpApi.Identity.Dtos.ProfileDto>} 用户信息
   * @returns {string} returns.id - 用户唯一标识
   * @returns {string} returns.username - 用户名
   * @returns {string} returns.email - 邮箱地址
   * @returns {string} returns.avatar - 头像URL
   * @returns {object} returns.profile - 其他用户资料信息
   * 
   * @throws {Error} 当访问令牌无效或用户不存在时抛出错误
   * 
   * @example
   * // 获取用户信息
   * try {
   *   const userInfo = await authService.getUserInfoAsync();
   *   
   *   console.log('用户信息:', userInfo);
   *   console.log('用户名:', userInfo.username);
   *   console.log('邮箱:', userInfo.email);
   * } catch (error) {
   *   console.error('获取用户信息失败:', error.message);
   * }
   * 
   * @description
   * 用户信息获取的使用场景：
   * 1. 应用启动时获取用户基本信息
   * 2. 用户资料页面的数据展示
   * 3. 权限验证和个性化设置
   * 4. 用户状态的实时同步
   * 
   * @note
   * - 该请求需要有效的访问令牌
   * - 请求会自动包含Authorization头
   * - 返回的用户信息可能根据权限有所不同
   * - 建议在令牌刷新后重新获取用户信息
   */
  async getUserInfoAsync(): Promise<HttpApi.Identity.Dtos.ProfileDto> {
    const { data } = await this.request({
      url: `${this.baseUrl}/userinfo`, // 用户信息获取端点
      method: 'get' // 使用GET方法获取数据
    });

    // 返回用户信息，断言data不为空
    return data!;
  }

  /**
   * 获取用户余额
   *
   * @description
   * 从服务器获取当前已认证用户的账户余额信息。
   * 该方法用于获取用户的当前可用余额，通常在登录后和投注操作后调用。
   *
   * @returns {Promise<number>}
   * 返回包含用户余额的Promise对象
   *
   * @throws {Error}
   * 当请求失败或用户未认证时抛出错误
   *
   * @example
   * ```typescript
   * try {
   *   const balance = await authService.getBalanceAsync();
   *   console.log('用户余额:', balance);
   * } catch (error) {
   *   console.error('获取余额失败:', error);
   * }
   * ```
   *
   * @since 1.0.0
   * @version 1.0.0
   *
   * @remarks
   * 该方法的主要用途包括：
   * 1. 用户登录后的余额初始化
   * 2. 投注前的余额验证
   * 3. 投注后的余额更新
   * 4. 余额变动的实时同步
   *
   * @note
   * - 该请求需要有效的访问令牌
   * - 请求会自动包含Authorization头
   * - 余额信息实时更新
   * - 建议在投注操作后重新获取余额
   * - 接口返回格式：[{"currencyId":"USDT","balance":49761.44}]
   */
  async getBalanceAsync(): Promise<number> {
    const { data } = await this.request({
      url: '/user/banance', // 用户余额获取端点
      method: 'get' // 使用GET方法获取数据
    });

    // 处理返回的数组格式，提取 USDT 余额
    if (Array.isArray(data) && data.length > 0) {
      const balanceArray = data as BalanceData[];
      // 查找 USDT 货币的余额
      const usdtBalance = balanceArray.find(item => item.currencyId === 'USDT');
      if (usdtBalance) {
        return usdtBalance.balance;
      }
      // 如果没有找到 USDT，返回第一个货币的余额
      return balanceArray[0].balance;
    }

    // 如果数据格式不正确，返回 0
    console.warn('余额数据格式不正确:', data);
    return 0;
  }
}
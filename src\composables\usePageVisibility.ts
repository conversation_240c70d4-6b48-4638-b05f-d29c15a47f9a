// composables/usePageVisibility.ts
import { onMounted, onUnmounted } from 'vue'

export function usePageVisibility(onBack: () => void) {
  let hasLeft = false

  function handleVisibilityChange() {
    const isHidden = document.visibilityState === 'hidden'
    if (isHidden) {
      hasLeft = true
    } else if (hasLeft) {
      hasLeft = false
      onBack() // 回来时触发刷新逻辑
    }
  }

  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)
  })

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })
}

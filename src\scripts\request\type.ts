// ========== 请求实例状态接口定义 ==========
/**
 * 请求实例状态接口
 * 用于管理HTTP请求过程中的各种状态信息，包括token刷新和错误消息管理
 * 
 * @description
 * 该接口定义了HTTP请求实例需要维护的核心状态：
 * 1. Token刷新状态管理 - 防止并发刷新和重复请求
 * 2. 错误消息堆栈 - 避免重复显示相同错误给用户
 * 
 * @example
 * const requestState: RequestInstanceState = {
 *   refreshTokenFn: null,
 *   errMsgStack: []
 * };
 */
export interface RequestInstanceState {
  /**
   * Token刷新函数的Promise对象
   * 
   * @description
   * 用于跟踪当前是否有正在进行的token刷新操作
   * - null: 当前没有进行token刷新
   * - Promise<boolean>: 正在进行token刷新，Promise解析为刷新是否成功
   * 
   * @purpose
   * 1. 防止并发token刷新：当多个请求同时遇到token过期时，只发起一次刷新请求
   * 2. 状态共享：所有等待的请求都可以等待同一个刷新Promise的结果
   * 3. 避免竞态条件：确保token刷新的原子性操作
   * 
   * @workflow
   * - 检测到token过期 → 检查refreshTokenFn是否为null
   * - 如果为null → 创建新的刷新Promise并赋值
   * - 如果不为null → 等待现有的刷新Promise完成
   * - 刷新完成后 → 将refreshTokenFn重置为null
   */
  refreshTokenFn: Promise<boolean> | null;

  /**
   * 请求错误消息堆栈
   * 
   * @description
   * 存储当前正在显示给用户的所有错误消息，用于防止重复显示相同的错误
   * 
   * @purpose
   * 1. 去重显示：避免相同错误消息的重复显示，提升用户体验
   * 2. 状态追踪：跟踪当前有哪些错误消息正在展示
   * 3. 内存管理：配合UI组件的生命周期，及时清理已消失的错误消息
   * 
   * @usage
   * - 显示错误前检查：if (!errMsgStack.includes(message)) { ... }
   * - 添加新错误：errMsgStack.push(message)
   * - 清理已消失的错误：errMsgStack = errMsgStack.filter(msg => msg !== message)
   * 
   * @example
   * // 典型的错误消息生命周期
   * state.errMsgStack = []; // 初始化
   * state.errMsgStack.push("网络连接失败"); // 添加错误
   * state.errMsgStack.push("登录已过期"); // 添加另一个错误
   * // 用户关闭错误提示后
   * state.errMsgStack = state.errMsgStack.filter(msg => msg !== "网络连接失败");
   */
  errMsgStack: string[];
}
import * as signalR from '@microsoft/signalr';

/** 封装一个SignalR类，用于连接SignalR服务 提供链接，断开，重连等方法 维持多个链接，链接不同的Hub 支持自定义事件监听 支持切换Hub 支持销毁 */
// 添加一个方法来更新令牌
let currentAccessToken = '';
export function updateSignalRToken(token: string) {
  currentAccessToken = token;
}
class SignalR {
  private connections: { [key: string]: signalR.HubConnection } = {};
  private onError: ((msg: string) => void) | null = null;
  private onReconnected: ((msg: string) => void) | null = null;
  private onReconnecting: ((msg: string) => void) | null = null;
  private connectionAttempts: { [key: string]: number } = {};
  private maxRetries = 5;
  private isReconnecting: { [key: string]: boolean } = {};

  /**
   * 设置错误处理回调
   * @param handler 错误处理函数
   */
  setErrorHandler(handler: (msg: string) => void) {
    this.onError = handler;
  }

  /**
   * 设置重连成功回调
   * @param handler 重连成功处理函数
   */
  setReconnectedHandler(handler: (msg: string) => void) {
    this.onReconnected = handler;
  }

  /**
   * 设置正在重连回调
   * @param handler 正在重连处理函数
   */
  setReconnectingHandler(handler: (msg: string) => void) {
    this.onReconnecting = handler;
  }

  

  /**
   * 连接到指定的Hub
   * @param hub Hub名称
   */
  connect(hub: string) {
    let connection = this.connections[hub];

    // 初始化连接尝试次数
    if (!this.connectionAttempts[hub]) {
      this.connectionAttempts[hub] = 0;
    }

    // 如果连接不存在，创建新连接
    if (!connection) {
      connection = new signalR.HubConnectionBuilder()
        .withUrl(`/hubs/${hub}`, {
          accessTokenFactory: () => {
            return currentAccessToken;
          }
        })
        .withAutomaticReconnect([0, 2000, 5000, 10000, 30000]) // 重试间隔时间递增
        .configureLogging(signalR.LogLevel.Information) // 增加日志级别
        .build();

      // 注册重连事件
      connection.onreconnecting((error) => {
        this.isReconnecting[hub] = true;
        console.warn(`[SignalR] ${hub} 连接断开，正在尝试重连...`, error);
        this.onReconnecting?.(`${hub} 连接断开，正在尝试重连...`);
      });

      // 注册重连成功事件
      connection.onreconnected((connectionId) => {
        this.isReconnecting[hub] = false;
        console.log(`[SignalR] ${hub} 重连成功，连接ID: ${connectionId}`);
        this.onReconnected?.(`${hub} 连接已恢复`);
        this.connectionAttempts[hub] = 0; // 重置重试计数
      });

      // 注册关闭事件
      connection.onclose((error) => {
        console.error(`[SignalR] ${hub} 连接已关闭`, error);

        // 如果不是正在重连状态，则尝试重新连接
        if (!this.isReconnecting[hub]) {
          this.connectionAttempts[hub] = 0; // 重置重试计数
          this.connectWithRetry(connection, hub);
        }
      });

      this.connections[hub] = connection;
    }

    // 如果连接已断开，则尝试连接
    if (connection.state === signalR.HubConnectionState.Disconnected) {
      this.connectWithRetry(connection, hub);
    }
  }

  /**
   * 带重试机制的连接
   * @param connection SignalR连接对象
   * @param hub Hub名称
   */
  private async connectWithRetry(connection: signalR.HubConnection, hub: string) {
    // 已经超过最大重试次数
    if (this.connectionAttempts[hub] >= this.maxRetries) {
      console.error(`[SignalR] ${hub} 连接失败，已达到最大重试次数(${this.maxRetries})`);
      // 确保错误处理器被调用
      if (this.onError) {
        this.onError(`网络连接失败，请检查网络连接后刷新页面`);
      }
      return;
    }
  
    try {
      this.connectionAttempts[hub]++;
      await connection.start();
      console.log(`[SignalR] ${hub} 连接成功`);
      this.connectionAttempts[hub] = 0; // 连接成功后重置计数器
    } catch (err) {
      const currentAttempt = this.connectionAttempts[hub];
      const delay = Math.min(1000 * Math.pow(2, currentAttempt), 30000); // 指数退避算法

      console.error(`[SignalR] 连接 ${hub} 失败 (${currentAttempt}/${this.maxRetries}):`, err);
      if (this.onError) {
        // 添加更详细的错误信息
        const errorMsg = err instanceof Error ? err.message : String(err);
        this.onError(`连接服务器失败: ${errorMsg}，${Math.ceil(delay/1000)}秒后重试(${currentAttempt}/${this.maxRetries})...`);
      }

      // 设置延迟后重试
      setTimeout(() => {
        // 检查网络连接
        if (!navigator.onLine) {
          this.onError?.('网络连接不可用，请检查网络设置');
          // 添加网络恢复事件监听
          const onlineHandler = () => {
            window.removeEventListener('online', onlineHandler);
            this.connectWithRetry(connection, hub);
          };
          window.addEventListener('online', onlineHandler);
          return;
        }

        // 继续尝试连接
        this.connectWithRetry(connection, hub);
      }, delay);
    }
  }

  /**
   * 断开指定Hub的连接
   * @param hub Hub名称
   */
  disconnect(hub: string) {
    const connection = this.connections[hub];
    if (connection && connection.state === signalR.HubConnectionState.Connected) {
      connection.stop()
        .then(() => console.log(`[SignalR] ${hub} 连接已断开`))
        .catch(err => console.error(`[SignalR] 断开 ${hub} 连接时出错`, err));
    }
  }

  /**
   * 监听事件
   * @param hub Hub名称
   * @param event 事件名称
   * @param handler 事件处理函数
   */
  on(hub: string, event: string, handler: (...args: any[]) => void) {
    const connection = this.connections[hub];
    if (!connection) {
      console.warn(`[SignalR] 无法监听事件，Hub ${hub} 不存在`);
      return;
    }

    switch (event) {
      case 'reconnecting':
        connection.onreconnecting(() => {
          handler();
        });
        break;
      case 'reconnected':
        connection.onreconnected(() => {
          handler();
        });
        break;
      case 'closed':
        connection.onclose(() => {
          handler();
        });
        break;
      default:
        connection.on(event, handler);
        break;
    }
  }

  /**
   * 取消监听事件
   * @param hub Hub名称
   * @param event 事件名称
   * @param handler 事件处理函数
   */
  off(hub: string, event: string, handler: (...args: any[]) => void) {
    const connection = this.connections[hub];
    if (!connection) {
      console.warn(`[SignalR] 无法取消监听事件，Hub ${hub} 不存在`);
      return;
    }

    try {
      connection.off(event, handler);
    } catch (err) {
      console.error(`[SignalR] 取消监听事件 ${event} 出错`, err);
    }
  }

  /**
   * 发送消息（无返回值）
   * @param hub Hub名称
   * @param event 事件名称
   * @param args 参数列表
   */
  send(hub: string, event: string, ...args: any[]) {
    const connection = this.connections[hub];
    if (!connection) {
      console.warn(`[SignalR] 无法发送消息，Hub ${hub} 不存在`);
      this.onError?.(`无法连接到服务器，请刷新页面重试`);
      return;
    }

    if (connection.state !== signalR.HubConnectionState.Connected) {
      console.warn(`[SignalR] 无法发送消息，Hub ${hub} 未连接`);
      this.onError?.(`与服务器的连接已断开，正在尝试重新连接...`);

      // 尝试重新连接
      if (connection.state === signalR.HubConnectionState.Disconnected) {
        this.connect(hub);
      }
      return;
    }

    try {
      connection.send(event, ...args)
        .catch(err => {
          console.error(`[SignalR] 发送消息 ${event} 失败`, err);
          this.onError?.(`发送消息失败，请重试`);
        });
    } catch (err) {
      console.error(`[SignalR] 发送消息 ${event} 出错`, err);
      this.onError?.(`发送消息失败，请重试`);
    }
  }

  /**
   * 发送消息并等待返回值
   * @param hub Hub名称
   * @param event 事件名称
   * @param args 参数列表
   * @returns Promise对象
   */
  invoke(hub: string, event: string, ...args: any[]) {
    const connection = this.connections[hub];
    if (!connection) {
      console.warn(`[SignalR] 无法调用方法，Hub ${hub} 不存在`);
      this.onError?.(`无法连接到服务器，请刷新页面重试`);
      return Promise.reject(new Error(`Hub ${hub} 不存在`));
    }

    if (connection.state !== signalR.HubConnectionState.Connected) {
      console.warn(`[SignalR] 无法调用方法，Hub ${hub} 未连接`);
      this.onError?.(`与服务器的连接已断开，正在尝试重新连接...`);

      // 尝试重新连接
      if (connection.state === signalR.HubConnectionState.Disconnected) {
        this.connect(hub);
      }
      return Promise.reject(new Error(`Hub ${hub} 未连接`));
    }

    // 添加超时处理
    const timeout = 30000; // 30秒超时
    return Promise.race([
      connection.invoke(event, ...args),
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`调用方法 ${event} 超时`)), timeout);
      })
    ]).catch(err => {
      console.error(`[SignalR] 调用方法 ${event} 失败`, err);
      this.onError?.(`请求超时或失败，请重试`);
      throw err;
    });
  }

  /**
   * 获取连接状态
   * @param hub Hub名称
   * @returns 连接状态
   */
  getConnectionState(hub: string): signalR.HubConnectionState {
    const connection = this.connections[hub];
    if (!connection) {
      return signalR.HubConnectionState.Disconnected;
    }
    return connection.state;
  }

  /**
   * 销毁所有连接
   */
  destroy() {
    for (const hub in this.connections) {
      if (Object.prototype.hasOwnProperty.call(this.connections, hub)) {
        try {
          this.connections[hub].stop().catch(err => {
            console.error(`[SignalR] 停止 ${hub} 连接时出错`, err);
          });
        } catch (err) {
          console.error(`[SignalR] 销毁 ${hub} 连接时出错`, err);
        }
      }
    }
    this.connections = {};
    this.connectionAttempts = {};
    this.isReconnecting = {};
    console.log('[SignalR] 所有连接已销毁');
  }
}

const socketClient = new SignalR();
export default socketClient;
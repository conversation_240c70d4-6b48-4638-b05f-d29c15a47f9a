<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useGameStore } from '@/stores/game'

const gameStore = useGameStore()

const radius = 20
const circumference = 2 * Math.PI * radius

const remaining = ref(gameStore.countdownSeconds)
const timer = ref<number | null>(null)

const progress = computed(() => {
  const total = gameStore.countdownSeconds
  return total > 0 ? (remaining.value / total) * circumference : 0
})

const start = () => {
  if (timer.value !== null) return
  timer.value = window.setInterval(() => {
    if (remaining.value > 0) {
      remaining.value--
    } else {
      stop()
    }
  }, 1000)
}

const stop = () => {
  if (timer.value !== null) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const reset = () => {
  stop()
  remaining.value = gameStore.countdownSeconds
  if (remaining.value > 0) start()
}

watch(() => gameStore.countdown, reset)

onMounted(() => {
  reset()
})

onUnmounted(() => {
  stop()
})
</script>

<template>
  <div class="relative w-[45px] h-[45px]">
    <svg class="w-full h-full rotate-[-90deg]" viewBox="0 0 50 50">
      <circle
        cx="25"
        cy="25"
        r="20"
        stroke="#e5e7eb3a"
        stroke-width="5"
        fill="none"
      />
      <circle
        cx="25"
        cy="25"
        r="20"
        stroke="#3b82f6"
        stroke-width="5"
        fill="none"
        stroke-linecap="round"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="circumference - progress"
      />
    </svg>
    <div class="absolute inset-0 flex items-center justify-center text-[16px] font-bold">
      {{ remaining }}
    </div>
  </div>
</template>

<style scoped>
svg {
  transform: rotate(0deg);
}
</style>

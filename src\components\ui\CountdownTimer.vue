<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useGameStore } from '@/stores/game'

const emit = defineEmits<{
  (e: 'finished'): void
}>()

const gameStore = useGameStore()

const radius = 20
const circumference = 2 * Math.PI * radius

const remaining = ref(0)
const timer = ref<number | null>(null)
const initialCountdown = ref(0) // 保存初始倒计时值

// 计算进度
const progress = computed(() => {
  return initialCountdown.value > 0 ? (remaining.value / initialCountdown.value) * circumference : 0
})

// 显示的文本内容
const displayText = computed(() => {
  if (gameStore.phase === 'stoping') {
    console.log('🔴 [CountdownTimer] 显示"封"字，剩余时间:', remaining.value)
    return '封'
  }
  console.log('🔵 [CountdownTimer] 显示倒计时:', remaining.value)
  return remaining.value.toString()
})

// 是否显示倒计时圆环
const showProgress = computed(() => {
  return gameStore.phase === 'opening' && remaining.value > 0
})

const start = () => {
  if (timer.value !== null) return
  timer.value = window.setInterval(() => {
    if (remaining.value > 0) {
      remaining.value--
    } else {
      stop()
      emit('finished')
    }
  }, 1000)
}

const stop = () => {
  if (timer.value !== null) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const reset = () => {
  stop()

  if (gameStore.phase === 'opening') {
    // OpeningAsync 信号：开始倒计时
    remaining.value = gameStore.countdownSeconds
    initialCountdown.value = gameStore.countdownSeconds
    if (remaining.value > 0) {
      start()
    }
  } else if (gameStore.phase === 'stoping') {
    // StopingAsync 信号：显示"封"字
    remaining.value = gameStore.countdownSeconds
    initialCountdown.value = gameStore.countdownSeconds
    if (remaining.value > 0) {
      start() // 继续倒计时，但显示"封"字
    }
  }
}

// 监听游戏状态和倒计时变化
watch(() => [gameStore.phase, gameStore.countdown], () => {
  console.log(`🕐 [CountdownTimer] 状态变化: ${gameStore.phase}, 倒计时: ${gameStore.countdown}`)
  reset()
}, { immediate: true })

onMounted(() => {
  reset()
})

onUnmounted(() => {
  stop()
})
</script>

<template>
  <div class="relative w-[45px] h-[45px]">
    <!-- 背景圆环 -->
    <svg class="w-full h-full transform -rotate-90" viewBox="0 0 50 50">
      <circle
        cx="25"
        cy="25"
        r="20"
        stroke="#e5e7eb3a"
        stroke-width="5"
        fill="none"
      />
      <!-- 进度圆环 - 只在 opening 状态且有倒计时时显示 -->
      <circle
        v-if="showProgress"
        cx="25"
        cy="25"
        r="20"
        stroke="#3b82f6"
        stroke-width="5"
        fill="none"
        stroke-linecap="round"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="circumference - progress"
        class="transition-all duration-1000"
      />
      <!-- 封盘状态的红色圆环 -->
      <circle
        v-if="gameStore.phase === 'stoping'"
        cx="25"
        cy="25"
        r="20"
        stroke="#ef4444"
        stroke-width="5"
        fill="none"
        stroke-linecap="round"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="circumference - progress"
        class="transition-all duration-1000"
      />
    </svg>

    <!-- 显示文本：倒计时数字或"封"字 -->
    <div
      class="absolute inset-0 flex items-center justify-center font-bold transition-all duration-300"
      :class="{
        'text-[16px] text-white': gameStore.phase === 'opening',
        'text-[18px] text-red-500': gameStore.phase === 'stoping',
        'text-[14px] text-gray-400': gameStore.phase !== 'opening' && gameStore.phase !== 'stoping'
      }"
    >
      {{ displayText }}
    </div>
  </div>
</template>

<style scoped>
svg {
  transform: rotate(0deg);
}
</style>
